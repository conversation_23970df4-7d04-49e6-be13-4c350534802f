version: '3.8'

services:
  # DynamoDB Local for development
  dynamodb-local:
    image: amazon/dynamodb-local:latest
    container_name: whatsapp-dynamodb-local
    ports:
      - "8000:8000"
    command: ["-jar", "DynamoDBLocal.jar", "-sharedDb", "-port", "8000"]
    volumes:
      - dynamodb-data:/home/<USER>/data
    working_dir: /home/<USER>
    environment:
      - AWS_ACCESS_KEY_ID=dummy
      - AWS_SECRET_ACCESS_KEY=dummy
      - AWS_DEFAULT_REGION=ap-southeast-1
    networks:
      - whatsapp-network

volumes:
  dynamodb-data:
    driver: local

networks:
  whatsapp-network:
    driver: bridge
