version: '3.8'

services:
  # DynamoDB Local for development
  dynamodb-local:
    image: amazon/dynamodb-local:latest
    container_name: whatsapp-dynamodb-local
    ports:
      - "8000:8000"
    command: ["-jar", "DynamoDBLocal.jar", "-sharedDb", "-optimizeDbBeforeStartup", "-dbPath", "./data"]
    volumes:
      - dynamodb-data:/home/<USER>/data
    working_dir: /home/<USER>
    environment:
      - AWS_ACCESS_KEY_ID=dummy
      - AWS_SECRET_ACCESS_KEY=dummy
      - AWS_DEFAULT_REGION=ap-southeast-1
    networks:
      - whatsapp-network
    healthcheck:
      test: ["CMD-SHELL", "curl -X POST http://localhost:8000 -H 'Content-Type: application/x-amz-json-1.0' -H 'X-Amz-Target: DynamoDB_20120810.ListTables' -d '{}' || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # DynamoDB Admin UI for easy table management
  dynamodb-admin:
    image: aaronshaf/dynamodb-admin:latest
    container_name: whatsapp-dynamodb-admin
    ports:
      - "8001:8001"
    environment:
      - DYNAMO_ENDPOINT=http://dynamodb-local:8000
      - AWS_REGION=ap-southeast-1
      - AWS_ACCESS_KEY_ID=dummy
      - AWS_SECRET_ACCESS_KEY=dummy
    depends_on:
      dynamodb-local:
        condition: service_healthy
    networks:
      - whatsapp-network

  # Redis for caching and rate limiting (optional)
  redis:
    image: redis:7-alpine
    container_name: whatsapp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - whatsapp-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  dynamodb-data:
    driver: local
  redis-data:
    driver: local

networks:
  whatsapp-network:
    driver: bridge
