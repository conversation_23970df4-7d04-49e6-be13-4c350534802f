{"name": "whatsapp-api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "ts-node src/index.ts", "dev": "nodemon --exec ts-node src/simple-server.ts", "dev:full": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start:legacy": "node whatsapp-web-server.js", "dev:legacy": "nodemon whatsapp-web-server.js", "local:start": "./scripts/start-local-dev.sh", "local:stop": "./scripts/stop-local-dev.sh", "local:test": "./scripts/test-dynamodb.sh", "local:reset": "docker-compose -f docker-compose.dev.yml down -v", "local:logs": "docker-compose -f docker-compose.dev.yml logs -f"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@adiwajshing/keyed-db": "^0.2.4", "@aws-sdk/client-dynamodb": "^3.830.0", "@aws-sdk/client-secrets-manager": "^3.830.0", "@aws-sdk/lib-dynamodb": "^3.830.0", "@whiskeysockets/baileys": "^6.2.1", "axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "joi": "^17.12.2", "qrcode": "^1.5.3", "qrcode-terminal": "^0.12.0", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.16.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/joi": "^17.2.3", "@types/node": "^22.15.14", "@types/qrcode-terminal": "^0.12.2", "@types/uuid": "^9.0.8", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}