# WhatsApp Manager Service - Local Development Configuration

# ===== BASIC CONFIGURATION =====
NODE_ENV=development
PORT=3000

# ===== AWS CONFIGURATION =====
AWS_REGION=ap-southeast-1
# For local development with DynamoDB Local, use dummy credentials
AWS_ACCESS_KEY_ID=dummy
AWS_SECRET_ACCESS_KEY=dummy
# AWS_PROFILE=default  # Commented out for local DynamoDB

# DynamoDB Local Configuration
DYNAMODB_ENDPOINT=http://localhost:8000
DYNAMODB_SESSION_TABLE=whatsapp-sessions-local
DYNAMODB_AUTH_STATE_TABLE=whatsapp-auth-state-local
DYNAMODB_CONFIG_TABLE=whatsapp-config-local
DYNAMODB_STATS_TABLE=whatsapp-stats-local

# ===== SESSION CONFIGURATION =====
SESSIONS_DIR=./sessions
SESSION_TIMEOUT_HOURS=24
MAX_SESSIONS_PER_USER=5
SESSION_CLEANUP_INTERVAL_SECONDS=3600

# ===== WEBHOOK CONFIGURATION =====
# Use ngrok or webhook.site for local testing
WEBHOOK_URL=
WEBHOOK_TIMEOUT_MS=10000
WEBHOOK_MAX_RETRIES=3
WEBHOOK_SECRET_KEY=local_webhook_secret

# ===== LOGGING CONFIGURATION =====
LOG_LEVEL=debug
LOG_DIR=./logs
LOG_MAX_FILES=7
LOG_MAX_SIZE=50mb

# ===== RATE LIMITING =====
RATE_LIMIT_MESSAGES_PER_MINUTE=30
BULK_MESSAGE_MAX_RECIPIENTS=10
BULK_MESSAGE_DEFAULT_DELAY=2000

# ===== MONITORING =====
ENABLE_METRICS=true
METRICS_COLLECTION_INTERVAL=300000

# ===== SECURITY =====
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8001
API_KEY=local_test_key