# WhatsApp Manager Service - Local Development Configuration

# ===== BASIC CONFIGURATION =====
NODE_ENV=development
PORT=3000

# ===== AWS CONFIGURATION =====
AWS_REGION=us-east-1
# Use AWS SSO for local development
AWS_PROFILE=default

# DynamoDB Configuration (using AWS, not local)
DYNAMODB_SESSION_TABLE=whatsapp-sessions-local
DYNAMODB_AUTH_STATE_TABLE=whatsapp-auth-states-local
# DYNAMODB_ENDPOINT=http://localhost:8000  # Commented out to use AWS DynamoDB

# ===== SESSION CONFIGURATION =====
SESSIONS_DIR=./sessions
SESSION_TIMEOUT_HOURS=24
MAX_SESSIONS_PER_USER=5
SESSION_CLEANUP_INTERVAL_SECONDS=3600

# ===== WEBHOOK CONFIGURATION =====
# Use ngrok or webhook.site for local testing
WEBHOOK_URL=
WEBHOOK_TIMEOUT_MS=10000
WEBHOOK_MAX_RETRIES=3
WEBHOOK_SECRET_KEY=local_webhook_secret

# ===== LOGGING CONFIGURATION =====
LOG_LEVEL=debug
LOG_DIR=./logs
LOG_MAX_FILES=7
LOG_MAX_SIZE=50mb

# ===== RATE LIMITING =====
RATE_LIMIT_MESSAGES_PER_MINUTE=30
BULK_MESSAGE_MAX_RECIPIENTS=10
BULK_MESSAGE_DEFAULT_DELAY=2000

# ===== MONITORING =====
ENABLE_METRICS=true
METRICS_COLLECTION_INTERVAL=300000

# ===== SECURITY =====
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8001
API_KEY=local_test_key