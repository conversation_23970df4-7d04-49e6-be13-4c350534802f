const express = require('express');

const app = express();
const port = 3001;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({
    service: 'WhatsApp Manager API',
    version: '2.0.0', 
    status: 'running',
    message: 'Simple test server is working!',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date() 
  });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Test server running at http://localhost:${port}`);
  console.log(`📋 Health check: http://localhost:${port}/health`);
  console.log(`🌍 Listening on all interfaces: 0.0.0.0:${port}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});

console.log('Starting test server...');