"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendWebhook = sendWebhook;
const axios_1 = __importDefault(require("axios"));
const config_1 = __importDefault(require("../config"));
function sendWebhook(payload, webhookUrl) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const url = webhookUrl || config_1.default.webhookUrl;
            if (!url || url.trim() === '') {
                console.log('No webhook URL configured, skipping webhook notification');
                return false;
            }
            yield axios_1.default.post(url, payload, {
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            return true;
        }
        catch (error) {
            console.error('Error sending webhook notification:', error);
            return false;
        }
    });
}
exports.default = sendWebhook;
//# sourceMappingURL=webhook.js.map