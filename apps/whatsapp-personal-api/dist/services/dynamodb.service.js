"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const config_1 = __importDefault(require("../config"));
const logger_1 = require("../utils/logger");
const logger = (0, logger_1.createComponentLogger)('DynamoDBService');
class DynamoDBService {
    constructor() {
        // Initialize DynamoDB client with SSO support
        this.client = new client_dynamodb_1.DynamoDBClient(Object.assign({ region: config_1.default.aws.region }, (config_1.default.aws.endpoint && { endpoint: config_1.default.aws.endpoint })));
        this.docClient = lib_dynamodb_1.DynamoDBDocumentClient.from(this.client);
        // Initialize tables
        this.initializeTables().catch(error => {
            logger.error('Failed to initialize DynamoDB tables', error);
        });
    }
    initializeTables() {
        return __awaiter(this, void 0, void 0, function* () {
            yield Promise.all([
                this.createSessionTableIfNotExists(),
                this.createAuthStateTableIfNotExists(),
                this.createConfigTableIfNotExists(),
                this.createStatsTableIfNotExists()
            ]);
        });
    }
    createSessionTableIfNotExists() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield this.client.send(new client_dynamodb_1.DescribeTableCommand({
                    TableName: config_1.default.dynamodb.sessionTableName
                }));
                logger.info('Session table already exists', { tableName: config_1.default.dynamodb.sessionTableName });
            }
            catch (error) {
                if (error instanceof client_dynamodb_1.ResourceNotFoundException) {
                    logger.info('Creating session table', { tableName: config_1.default.dynamodb.sessionTableName });
                    yield this.client.send(new client_dynamodb_1.CreateTableCommand({
                        TableName: config_1.default.dynamodb.sessionTableName,
                        KeySchema: [
                            { AttributeName: 'sessionId', KeyType: 'HASH' }
                        ],
                        AttributeDefinitions: [
                            { AttributeName: 'sessionId', AttributeType: 'S' },
                            { AttributeName: 'userId', AttributeType: 'S' },
                            { AttributeName: 'status', AttributeType: 'S' }
                        ],
                        GlobalSecondaryIndexes: [
                            {
                                IndexName: 'UserIndex',
                                KeySchema: [
                                    { AttributeName: 'userId', KeyType: 'HASH' },
                                    { AttributeName: 'sessionId', KeyType: 'RANGE' }
                                ],
                                Projection: { ProjectionType: 'ALL' }
                            },
                            {
                                IndexName: 'StatusIndex',
                                KeySchema: [
                                    { AttributeName: 'status', KeyType: 'HASH' },
                                    { AttributeName: 'sessionId', KeyType: 'RANGE' }
                                ],
                                Projection: { ProjectionType: 'ALL' }
                            }
                        ],
                        BillingMode: 'PAY_PER_REQUEST',
                        StreamSpecification: {
                            StreamEnabled: true,
                            StreamViewType: 'NEW_AND_OLD_IMAGES'
                        }
                    }));
                    logger.info('Session table created successfully', { tableName: config_1.default.dynamodb.sessionTableName });
                }
                else {
                    throw error;
                }
            }
        });
    }
    createAuthStateTableIfNotExists() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield this.client.send(new client_dynamodb_1.DescribeTableCommand({
                    TableName: config_1.default.dynamodb.authStateTableName
                }));
                logger.info('Auth state table already exists', { tableName: config_1.default.dynamodb.authStateTableName });
            }
            catch (error) {
                if (error instanceof client_dynamodb_1.ResourceNotFoundException) {
                    logger.info('Creating auth state table', { tableName: config_1.default.dynamodb.authStateTableName });
                    yield this.client.send(new client_dynamodb_1.CreateTableCommand({
                        TableName: config_1.default.dynamodb.authStateTableName,
                        KeySchema: [
                            { AttributeName: 'sessionId', KeyType: 'HASH' },
                            { AttributeName: 'keyType', KeyType: 'RANGE' }
                        ],
                        AttributeDefinitions: [
                            { AttributeName: 'sessionId', AttributeType: 'S' },
                            { AttributeName: 'keyType', AttributeType: 'S' }
                        ],
                        BillingMode: 'PAY_PER_REQUEST'
                    }));
                    logger.info('Auth state table created successfully', { tableName: config_1.default.dynamodb.authStateTableName });
                }
                else {
                    throw error;
                }
            }
        });
    }
    createConfigTableIfNotExists() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield this.client.send(new client_dynamodb_1.DescribeTableCommand({
                    TableName: config_1.default.dynamodb.configTableName
                }));
                logger.info('Config table already exists', { tableName: config_1.default.dynamodb.configTableName });
            }
            catch (error) {
                if (error instanceof client_dynamodb_1.ResourceNotFoundException) {
                    logger.info('Creating config table', { tableName: config_1.default.dynamodb.configTableName });
                    yield this.client.send(new client_dynamodb_1.CreateTableCommand({
                        TableName: config_1.default.dynamodb.configTableName,
                        KeySchema: [
                            { AttributeName: 'configKey', KeyType: 'HASH' }
                        ],
                        AttributeDefinitions: [
                            { AttributeName: 'configKey', AttributeType: 'S' }
                        ],
                        BillingMode: 'PAY_PER_REQUEST'
                    }));
                    logger.info('Config table created successfully', { tableName: config_1.default.dynamodb.configTableName });
                }
                else {
                    throw error;
                }
            }
        });
    }
    createStatsTableIfNotExists() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield this.client.send(new client_dynamodb_1.DescribeTableCommand({
                    TableName: config_1.default.dynamodb.statsTableName
                }));
                logger.info('Stats table already exists', { tableName: config_1.default.dynamodb.statsTableName });
            }
            catch (error) {
                if (error instanceof client_dynamodb_1.ResourceNotFoundException) {
                    logger.info('Creating stats table', { tableName: config_1.default.dynamodb.statsTableName });
                    yield this.client.send(new client_dynamodb_1.CreateTableCommand({
                        TableName: config_1.default.dynamodb.statsTableName,
                        KeySchema: [
                            { AttributeName: 'sessionId', KeyType: 'HASH' },
                            { AttributeName: 'date', KeyType: 'RANGE' }
                        ],
                        AttributeDefinitions: [
                            { AttributeName: 'sessionId', AttributeType: 'S' },
                            { AttributeName: 'date', AttributeType: 'S' }
                        ],
                        BillingMode: 'PAY_PER_REQUEST'
                    }));
                    logger.info('Stats table created successfully', { tableName: config_1.default.dynamodb.statsTableName });
                }
                else {
                    throw error;
                }
            }
        });
    }
    // Session Management Methods
    createSession(sessionData) {
        return __awaiter(this, void 0, void 0, function* () {
            const now = Date.now();
            const record = Object.assign(Object.assign({}, sessionData), { createdAt: now, updatedAt: now });
            yield this.docClient.send(new lib_dynamodb_1.PutCommand({
                TableName: config_1.default.dynamodb.sessionTableName,
                Item: record,
                ConditionExpression: 'attribute_not_exists(sessionId)'
            }));
            logger.info('Session created in DynamoDB', { sessionId: sessionData.sessionId, userId: sessionData.userId });
            return record;
        });
    }
    getSession(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.docClient.send(new lib_dynamodb_1.GetCommand({
                TableName: config_1.default.dynamodb.sessionTableName,
                Key: { sessionId }
            }));
            return result.Item || null;
        });
    }
    updateSession(sessionId, updates) {
        return __awaiter(this, void 0, void 0, function* () {
            const updateExpression = [];
            const expressionAttributeNames = {};
            const expressionAttributeValues = {};
            // Always update the updatedAt timestamp
            updates.updatedAt = Date.now();
            Object.entries(updates).forEach(([key, value]) => {
                if (value !== undefined) {
                    updateExpression.push(`#${key} = :${key}`);
                    expressionAttributeNames[`#${key}`] = key;
                    expressionAttributeValues[`:${key}`] = value;
                }
            });
            if (updateExpression.length === 0) {
                return;
            }
            yield this.docClient.send(new lib_dynamodb_1.UpdateCommand({
                TableName: config_1.default.dynamodb.sessionTableName,
                Key: { sessionId },
                UpdateExpression: `SET ${updateExpression.join(', ')}`,
                ExpressionAttributeNames: expressionAttributeNames,
                ExpressionAttributeValues: expressionAttributeValues
            }));
            logger.debug('Session updated in DynamoDB', { sessionId, updates: Object.keys(updates) });
        });
    }
    deleteSession(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.docClient.send(new lib_dynamodb_1.DeleteCommand({
                TableName: config_1.default.dynamodb.sessionTableName,
                Key: { sessionId }
            }));
            logger.info('Session deleted from DynamoDB', { sessionId });
        });
    }
    getAllSessions() {
        return __awaiter(this, arguments, void 0, function* (options = {}) {
            const { page = 1, limit = 10 } = options;
            const result = yield this.docClient.send(new lib_dynamodb_1.ScanCommand({
                TableName: config_1.default.dynamodb.sessionTableName,
                Limit: limit * page // Simple pagination for scan
            }));
            const items = result.Items || [];
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedItems = items.slice(startIndex, endIndex);
            return {
                items: paginatedItems,
                pagination: {
                    page,
                    limit,
                    total: items.length,
                    totalPages: Math.ceil(items.length / limit),
                    hasNext: endIndex < items.length,
                    hasPrev: page > 1
                }
            };
        });
    }
    getSessionsByUser(userId_1) {
        return __awaiter(this, arguments, void 0, function* (userId, options = {}) {
            const { page = 1, limit = 10 } = options;
            const result = yield this.docClient.send(new lib_dynamodb_1.QueryCommand({
                TableName: config_1.default.dynamodb.sessionTableName,
                IndexName: 'UserIndex',
                KeyConditionExpression: 'userId = :userId',
                ExpressionAttributeValues: {
                    ':userId': userId
                },
                Limit: limit,
                ScanIndexForward: false // Most recent first
            }));
            const items = result.Items || [];
            return {
                items,
                pagination: {
                    page,
                    limit,
                    total: items.length,
                    totalPages: Math.ceil(items.length / limit),
                    hasNext: !!result.LastEvaluatedKey,
                    hasPrev: page > 1
                }
            };
        });
    }
    getSessionsByStatus(status) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.docClient.send(new lib_dynamodb_1.QueryCommand({
                TableName: config_1.default.dynamodb.sessionTableName,
                IndexName: 'StatusIndex',
                KeyConditionExpression: 'status = :status',
                ExpressionAttributeValues: {
                    ':status': status
                }
            }));
            return result.Items || [];
        });
    }
    updateSessionMetadata(sessionId, metadata) {
        return __awaiter(this, void 0, void 0, function* () {
            const session = yield this.getSession(sessionId);
            if (!session) {
                throw new Error(`Session ${sessionId} not found`);
            }
            const updatedMetadata = Object.assign(Object.assign({}, session.metadata), metadata);
            yield this.updateSession(sessionId, { metadata: updatedMetadata });
        });
    }
    // Config Management Methods
    getConfig(key) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.docClient.send(new lib_dynamodb_1.GetCommand({
                TableName: config_1.default.dynamodb.configTableName,
                Key: { configKey: key }
            }));
            return result.Item || null;
        });
    }
    setConfig(key, value) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.docClient.send(new lib_dynamodb_1.PutCommand({
                TableName: config_1.default.dynamodb.configTableName,
                Item: {
                    configKey: key,
                    configValue: value,
                    updatedAt: Date.now()
                }
            }));
        });
    }
    // Stats Management Methods
    recordSessionStats(sessionId, date, stats) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.docClient.send(new lib_dynamodb_1.PutCommand({
                TableName: config_1.default.dynamodb.statsTableName,
                Item: Object.assign(Object.assign({ sessionId,
                    date }, stats), { updatedAt: Date.now() })
            }));
        });
    }
    getSessionStats(sessionId, startDate, endDate) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield this.docClient.send(new lib_dynamodb_1.QueryCommand({
                TableName: config_1.default.dynamodb.statsTableName,
                KeyConditionExpression: 'sessionId = :sessionId AND #date BETWEEN :startDate AND :endDate',
                ExpressionAttributeNames: {
                    '#date': 'date'
                },
                ExpressionAttributeValues: {
                    ':sessionId': sessionId,
                    ':startDate': startDate,
                    ':endDate': endDate
                }
            }));
            return result.Items || [];
        });
    }
    // Health Check
    healthCheck() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield this.docClient.send(new lib_dynamodb_1.GetCommand({
                    TableName: config_1.default.dynamodb.sessionTableName,
                    Key: { sessionId: 'health-check-' + Date.now() }
                }));
                return true;
            }
            catch (error) {
                logger.error('DynamoDB health check failed', error);
                return false;
            }
        });
    }
}
// Create singleton instance
const dynamoDBService = new DynamoDBService();
exports.default = dynamoDBService;
//# sourceMappingURL=dynamodb.service.js.map