"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const baileys_1 = __importStar(require("@whiskeysockets/baileys"));
// import { Boom } from '@hapi/boom';
const qrcode_terminal_1 = __importDefault(require("qrcode-terminal"));
const fs_1 = __importDefault(require("fs"));
const types_1 = require("../types");
const config_1 = __importDefault(require("../config"));
const webhook_1 = require("../utils/webhook");
const dynamodb_service_1 = __importDefault(require("./dynamodb.service"));
const dynamodb_auth_state_service_1 = require("./dynamodb-auth-state.service");
const logger_1 = require("../utils/logger");
class EnhancedWhatsAppService {
    constructor() {
        this.sessions = new Map();
        this.isShuttingDown = false;
        this.initializeService();
        this.startSessionCleanup();
    }
    initializeService() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Create sessions directory if it doesn't exist (fallback)
                if (!fs_1.default.existsSync(config_1.default.sessionsDir)) {
                    fs_1.default.mkdirSync(config_1.default.sessionsDir, { recursive: true });
                }
                // Load existing sessions from DynamoDB
                yield this.loadExistingSessions();
                logger_1.whatsappLogger.serviceStarted();
            }
            catch (error) {
                logger_1.whatsappLogger.error('Failed to initialize WhatsApp service', error);
                throw error;
            }
        });
    }
    loadExistingSessions() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { items: sessionRecords } = yield dynamodb_service_1.default.getAllSessions({ limit: 100 });
                for (const record of sessionRecords) {
                    if (record.status === types_1.SessionStatus.CONNECTED) {
                        try {
                            // Attempt to restore connected sessions
                            yield this.restoreSession(record.sessionId);
                        }
                        catch (error) {
                            logger_1.whatsappLogger.warn('Failed to restore session, marking as disconnected', {
                                sessionId: record.sessionId,
                                error: error instanceof Error ? error.message : String(error)
                            });
                            // Mark session as disconnected
                            yield dynamodb_service_1.default.updateSession(record.sessionId, {
                                status: types_1.SessionStatus.DISCONNECTED,
                                connectionAttempts: record.connectionAttempts + 1
                            });
                        }
                    }
                }
                logger_1.whatsappLogger.info('Existing sessions loaded', { count: sessionRecords.length });
            }
            catch (error) {
                logger_1.whatsappLogger.error('Failed to load existing sessions', error);
            }
        });
    }
    restoreSession(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const sessionRecord = yield dynamodb_service_1.default.getSession(sessionId);
                if (!sessionRecord) {
                    throw new Error('Session record not found');
                }
                const logger = (0, logger_1.createSessionLogger)(sessionId, sessionRecord.userId);
                // Create auth state service
                const authStateService = (0, dynamodb_auth_state_service_1.createDynamoDBAuthState)(sessionId);
                const { state } = authStateService.createAuthState();
                // Create WhatsApp socket
                const sock = yield this.createWhatsAppSocket(state);
                const session = {
                    id: sessionId,
                    name: sessionRecord.sessionName,
                    userId: sessionRecord.userId,
                    client: sock,
                    qr: sessionRecord.qrCode,
                    ready: false,
                    status: types_1.SessionStatus.PENDING,
                    phoneNumber: sessionRecord.phoneNumber,
                    webhookUrl: sessionRecord.webhookUrl,
                    createdAt: new Date(sessionRecord.createdAt),
                    lastSeen: sessionRecord.lastSeen ? new Date(sessionRecord.lastSeen) : undefined,
                    connectionAttempts: sessionRecord.connectionAttempts,
                    metadata: sessionRecord.metadata
                };
                // Set up event handlers
                this.setupSessionEventHandlers(session, logger, authStateService);
                // Store in memory
                this.sessions.set(sessionId, session);
                logger.info('Session restored successfully');
                return session;
            }
            catch (error) {
                const logger = (0, logger_1.createSessionLogger)(sessionId);
                logger.error('Failed to restore session', error);
                throw error;
            }
        });
    }
    // ===== PUBLIC SESSION MANAGEMENT METHODS =====
    createSession(request) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id: sessionId, userId, phoneNumber, name, webhookUrl } = request;
            const logger = (0, logger_1.createSessionLogger)(sessionId, userId);
            try {
                // Check if session already exists
                if (this.sessions.has(sessionId)) {
                    const existingSession = this.sessions.get(sessionId);
                    logger.warn('Session already exists', { status: existingSession.status });
                    return existingSession;
                }
                // Check user session limits
                yield this.checkUserSessionLimits(userId);
                logger.sessionCreated();
                // Create session record in DynamoDB
                const sessionRecord = yield dynamodb_service_1.default.createSession({
                    sessionId,
                    userId,
                    sessionName: name,
                    phoneNumber,
                    status: types_1.SessionStatus.PENDING,
                    webhookUrl,
                    expiresAt: Date.now() + (config_1.default.session.timeoutHours * 60 * 60 * 1000),
                    connectionAttempts: 0,
                    metadata: {
                        messagesSent: 0,
                        messagesReceived: 0,
                        webhookCallsSuccess: 0,
                        webhookCallsFailed: 0,
                        uptime: 0
                    }
                });
                // Create auth state service
                const authStateService = (0, dynamodb_auth_state_service_1.createDynamoDBAuthState)(sessionId);
                const { state } = authStateService.createAuthState();
                // Create WhatsApp socket
                const sock = yield this.createWhatsAppSocket(state);
                const session = {
                    id: sessionId,
                    name,
                    userId,
                    client: sock,
                    ready: false,
                    status: types_1.SessionStatus.PENDING,
                    phoneNumber,
                    webhookUrl,
                    createdAt: new Date(),
                    connectionAttempts: 0,
                    metadata: sessionRecord.metadata
                };
                // Set up event handlers
                this.setupSessionEventHandlers(session, logger, authStateService);
                // Store in memory
                this.sessions.set(sessionId, session);
                logger.info('Session created successfully');
                return session;
            }
            catch (error) {
                logger.error('Failed to create session', error);
                throw error;
            }
        });
    }
    getSession(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            // Check memory first
            let session = this.sessions.get(sessionId);
            if (!session) {
                // Try to restore from DynamoDB
                try {
                    session = yield this.restoreSession(sessionId);
                }
                catch (error) {
                    logger_1.whatsappLogger.debug('Session not found or could not be restored', { sessionId });
                    return undefined;
                }
            }
            return session !== null && session !== void 0 ? session : undefined;
        });
    }
    getAllSessions() {
        return Array.from(this.sessions.values());
    }
    updateSession(sessionId, updates) {
        return __awaiter(this, void 0, void 0, function* () {
            const session = yield this.getSession(sessionId);
            if (!session) {
                throw new Error(`Session ${sessionId} not found`);
            }
            const logger = (0, logger_1.createSessionLogger)(sessionId, session.userId);
            try {
                // Update session in DynamoDB
                yield dynamodb_service_1.default.updateSession(sessionId, {
                    sessionName: updates.name,
                    webhookUrl: updates.webhookUrl
                });
                // Update in-memory session
                if (updates.name !== undefined) {
                    session.name = updates.name;
                }
                if (updates.webhookUrl !== undefined) {
                    session.webhookUrl = updates.webhookUrl;
                }
                logger.info('Session updated', { updates: Object.keys(updates) });
                return session;
            }
            catch (error) {
                logger.error('Failed to update session', error);
                throw error;
            }
        });
    }
    deleteSession(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const session = this.sessions.get(sessionId);
            const logger = (0, logger_1.createSessionLogger)(sessionId, session === null || session === void 0 ? void 0 : session.userId);
            try {
                if (session) {
                    // Close WhatsApp connection
                    session.client.end(undefined);
                    // Remove from memory
                    this.sessions.delete(sessionId);
                    logger.sessionDeleted('manual_deletion');
                }
                // Delete from DynamoDB
                yield dynamodb_service_1.default.deleteSession(sessionId);
                // Clear auth state
                const authStateService = (0, dynamodb_auth_state_service_1.createDynamoDBAuthState)(sessionId);
                yield authStateService.clearAuthState();
                // Send webhook notification
                if (session === null || session === void 0 ? void 0 : session.webhookUrl) {
                    yield this.sendWebhookNotification(session, types_1.WebhookEvent.DELETED, {
                        reason: 'manual_deletion'
                    });
                }
                logger.info('Session deleted successfully');
                return true;
            }
            catch (error) {
                logger.error('Failed to delete session', error);
                return false;
            }
        });
    }
    reconnectSession(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const session = yield this.getSession(sessionId);
            if (!session) {
                throw new Error(`Session ${sessionId} not found`);
            }
            const logger = (0, logger_1.createSessionLogger)(sessionId, session.userId);
            try {
                // Close existing connection
                session.client.end(undefined);
                // Update connection attempts
                session.connectionAttempts += 1;
                yield dynamodb_service_1.default.updateSession(sessionId, {
                    connectionAttempts: session.connectionAttempts,
                    status: types_1.SessionStatus.PENDING
                });
                logger.connectionAttempt(session.connectionAttempts, 5);
                // Create new auth state and socket
                const authStateService = (0, dynamodb_auth_state_service_1.createDynamoDBAuthState)(sessionId);
                const { state } = authStateService.createAuthState();
                const sock = yield this.createWhatsAppSocket(state);
                // Update session
                session.client = sock;
                session.ready = false;
                session.status = types_1.SessionStatus.PENDING;
                // Set up event handlers
                this.setupSessionEventHandlers(session, logger, authStateService);
                logger.info('Session reconnection initiated');
                return session;
            }
            catch (error) {
                logger.error('Failed to reconnect session', error);
                throw error;
            }
        });
    }
    // ===== MESSAGING METHODS =====
    sendTextMessage(sessionId, to, text) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            const session = yield this.getSession(sessionId);
            if (!session || !session.ready) {
                throw new Error('Session not found or not ready');
            }
            const logger = (0, logger_1.createSessionLogger)(sessionId, session.userId);
            try {
                const formattedNumber = this.formatPhoneNumber(to);
                const result = yield session.client.sendMessage(formattedNumber, { text });
                // Update metadata
                yield this.updateSessionMetadata(sessionId, { messagesSent: session.metadata.messagesSent + 1 });
                logger.messageSent(to, 'text', ((_a = result === null || result === void 0 ? void 0 : result.key) === null || _a === void 0 ? void 0 : _a.id) || undefined);
                return result;
            }
            catch (error) {
                logger.error('Failed to send text message', error, { to });
                throw error;
            }
        });
    }
    sendMediaMessage(sessionId, to, url, caption, mimetype) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            const session = yield this.getSession(sessionId);
            if (!session || !session.ready) {
                throw new Error('Session not found or not ready');
            }
            const logger = (0, logger_1.createSessionLogger)(sessionId, session.userId);
            try {
                const formattedNumber = this.formatPhoneNumber(to);
                const mediaMessage = {
                    caption: caption || ''
                };
                // Determine media type based on mimetype
                if (mimetype === null || mimetype === void 0 ? void 0 : mimetype.startsWith('image/')) {
                    mediaMessage.image = { url };
                }
                else if (mimetype === null || mimetype === void 0 ? void 0 : mimetype.startsWith('video/')) {
                    mediaMessage.video = { url };
                }
                else if (mimetype === null || mimetype === void 0 ? void 0 : mimetype.startsWith('audio/')) {
                    mediaMessage.audio = { url };
                }
                else {
                    mediaMessage.document = { url, mimetype: mimetype || 'application/octet-stream' };
                }
                const result = yield session.client.sendMessage(formattedNumber, mediaMessage);
                // Update metadata
                yield this.updateSessionMetadata(sessionId, { messagesSent: session.metadata.messagesSent + 1 });
                logger.messageSent(to, 'media', ((_a = result === null || result === void 0 ? void 0 : result.key) === null || _a === void 0 ? void 0 : _a.id) || undefined);
                return result;
            }
            catch (error) {
                logger.error('Failed to send media message', error, { to, url });
                throw error;
            }
        });
    }
    sendBulkMessages(sessionId, request) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            const session = yield this.getSession(sessionId);
            if (!session || !session.ready) {
                throw new Error('Session not found or not ready');
            }
            const logger = (0, logger_1.createSessionLogger)(sessionId, session.userId);
            const { recipients, message, delay: messageDelay = 1000 } = request;
            logger.bulkMessageStarted(recipients.length);
            const results = [];
            let sent = 0;
            let failed = 0;
            for (const recipient of recipients) {
                try {
                    let result;
                    if (message.text) {
                        result = yield this.sendTextMessage(sessionId, recipient, message.text);
                    }
                    else if (message.media) {
                        result = yield this.sendMediaMessage(sessionId, recipient, message.media.url, message.media.caption, message.media.mimetype);
                    }
                    else {
                        throw new Error('Message must contain either text or media');
                    }
                    results.push({
                        to: recipient,
                        success: true,
                        messageId: (_a = result === null || result === void 0 ? void 0 : result.key) === null || _a === void 0 ? void 0 : _a.id
                    });
                    sent++;
                    // Delay between messages to avoid rate limiting
                    if (messageDelay > 0 && recipient !== recipients[recipients.length - 1]) {
                        yield (0, baileys_1.delay)(messageDelay);
                    }
                }
                catch (error) {
                    results.push({
                        to: recipient,
                        success: false,
                        error: error instanceof Error ? error.message : String(error)
                    });
                    failed++;
                    logger.error('Failed to send bulk message to recipient', error, { recipient });
                }
            }
            logger.bulkMessageCompleted(sent, failed, recipients.length);
            return {
                success: sent > 0,
                results,
                summary: {
                    total: recipients.length,
                    sent,
                    failed
                }
            };
        });
    }
    // ===== HEALTH AND MONITORING METHODS =====
    getSessionHealth(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const session = yield this.getSession(sessionId);
            if (!session) {
                throw new Error(`Session ${sessionId} not found`);
            }
            const uptime = session.createdAt ? Math.floor((Date.now() - session.createdAt.getTime()) / 1000) : 0;
            const issues = [];
            // Health checks
            if (session.connectionAttempts > 3) {
                issues.push('High connection attempt count');
            }
            if (session.status === types_1.SessionStatus.FAILED) {
                issues.push('Session in failed state');
            }
            if (session.lastSeen && (Date.now() - session.lastSeen.getTime()) > 5 * 60 * 1000) {
                issues.push('No recent activity');
            }
            return {
                sessionId,
                status: session.status,
                uptime,
                lastSeen: session.lastSeen,
                connectionAttempts: session.connectionAttempts,
                isHealthy: issues.length === 0 && session.status === types_1.SessionStatus.CONNECTED,
                issues
            };
        });
    }
    getSessionStats(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            const session = yield this.getSession(sessionId);
            if (!session) {
                throw new Error(`Session ${sessionId} not found`);
            }
            const today = new Date().toISOString().split('T')[0];
            const statsRecords = yield dynamodb_service_1.default.getSessionStats(sessionId, today, today);
            const todayStats = statsRecords[0];
            const uptime = session.createdAt ? Math.floor((Date.now() - session.createdAt.getTime()) / 1000) : 0;
            // Calculate webhook success rate
            const totalWebhookCalls = session.metadata.webhookCallsSuccess + session.metadata.webhookCallsFailed;
            const webhookSuccessRate = totalWebhookCalls > 0
                ? (session.metadata.webhookCallsSuccess / totalWebhookCalls) * 100
                : 100;
            return {
                sessionId,
                totalMessages: session.metadata.messagesSent + session.metadata.messagesReceived,
                messagesSentToday: (todayStats === null || todayStats === void 0 ? void 0 : todayStats.messagesSent) || 0,
                messagesReceivedToday: (todayStats === null || todayStats === void 0 ? void 0 : todayStats.messagesReceived) || 0,
                webhookSuccessRate,
                averageResponseTime: 0, // Could be implemented with response time tracking
                uptime,
                lastActivity: session.lastSeen
            };
        });
    }
    // ===== PRIVATE HELPER METHODS =====
    createWhatsAppSocket(state) {
        return __awaiter(this, void 0, void 0, function* () {
            const { version } = yield (0, baileys_1.fetchLatestBaileysVersion)();
            return (0, baileys_1.default)({
                auth: {
                    creds: state.creds,
                    keys: (0, baileys_1.makeCacheableSignalKeyStore)(state.keys, { level: 'silent' })
                },
                version,
                printQRInTerminal: config_1.default.environment === 'development',
                browser: ['WhatsApp Manager', 'Chrome', '103.0.5060.114'],
                connectTimeoutMs: 30000,
                defaultQueryTimeoutMs: 60000,
                keepAliveIntervalMs: 10000,
                generateHighQualityLinkPreview: true,
                syncFullHistory: false,
                markOnlineOnConnect: true
            });
        });
    }
    setupSessionEventHandlers(session, logger, authStateService) {
        const { client } = session;
        // Connection events
        client.ev.on('connection.update', (update) => __awaiter(this, void 0, void 0, function* () {
            yield this.handleConnectionUpdate(session, update, logger);
        }));
        // Credentials update
        client.ev.on('creds.update', () => __awaiter(this, void 0, void 0, function* () {
            try {
                yield authStateService.saveAuthState();
            }
            catch (error) {
                logger.error('Failed to save credentials', error);
            }
        }));
        // Message events
        client.ev.on('messages.upsert', (m) => __awaiter(this, void 0, void 0, function* () {
            yield this.handleMessagesUpsert(session, m, logger);
        }));
        // Presence events
        client.ev.on('presence.update', (update) => {
            logger.debug('Presence update', { update });
        });
    }
    handleConnectionUpdate(session, update, logger) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d, _e, _f;
            const { connection, lastDisconnect, qr } = update;
            if (qr) {
                logger.qrGenerated(qr);
                session.qr = qr;
                session.status = types_1.SessionStatus.PENDING;
                // Update in DynamoDB
                yield dynamodb_service_1.default.updateSession(session.id, {
                    qrCode: qr,
                    status: types_1.SessionStatus.PENDING
                });
                // Send webhook
                yield this.sendWebhookNotification(session, types_1.WebhookEvent.QR, { qr });
                // Display QR in terminal for development
                if (config_1.default.environment === 'development') {
                    qrcode_terminal_1.default.generate(qr, { small: true });
                }
            }
            if (connection === 'close') {
                const shouldReconnect = ((_b = (_a = lastDisconnect === null || lastDisconnect === void 0 ? void 0 : lastDisconnect.error) === null || _a === void 0 ? void 0 : _a.output) === null || _b === void 0 ? void 0 : _b.statusCode) !== baileys_1.DisconnectReason.loggedOut;
                const reason = (lastDisconnect === null || lastDisconnect === void 0 ? void 0 : lastDisconnect.error) ? (_c = lastDisconnect.error.output) === null || _c === void 0 ? void 0 : _c.statusCode : 'unknown';
                if (shouldReconnect && session.connectionAttempts < 5 && !this.isShuttingDown) {
                    logger.warn('Connection closed, attempting reconnect', { reason, attempt: session.connectionAttempts + 1 });
                    // Wait before reconnecting
                    setTimeout(() => {
                        this.reconnectSession(session.id).catch(error => {
                            logger.error('Auto-reconnect failed', error);
                        });
                    }, Math.min(session.connectionAttempts * 5000, 30000));
                }
                else {
                    logger.warn('Connection closed permanently', { reason });
                    session.ready = false;
                    session.status = reason === baileys_1.DisconnectReason.loggedOut ? types_1.SessionStatus.DISCONNECTED : types_1.SessionStatus.FAILED;
                    // Update in DynamoDB
                    yield dynamodb_service_1.default.updateSession(session.id, {
                        status: session.status,
                        connectionAttempts: session.connectionAttempts
                    });
                    // Send webhook
                    yield this.sendWebhookNotification(session, types_1.WebhookEvent.DISCONNECTED, { reason });
                    if (reason === baileys_1.DisconnectReason.loggedOut) {
                        // Clean up logged out session
                        this.sessions.delete(session.id);
                    }
                }
            }
            else if (connection === 'open') {
                logger.connected((_d = session.client.user) === null || _d === void 0 ? void 0 : _d.id);
                session.ready = true;
                session.status = types_1.SessionStatus.CONNECTED;
                session.lastSeen = new Date();
                session.phoneNumber = (_f = (_e = session.client.user) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.split(':')[0];
                // Reset connection attempts
                session.connectionAttempts = 0;
                // Update in DynamoDB
                yield dynamodb_service_1.default.updateSession(session.id, {
                    status: types_1.SessionStatus.CONNECTED,
                    phoneNumber: session.phoneNumber,
                    connectionAttempts: 0,
                    lastSeen: Date.now()
                });
                // Send webhook
                yield this.sendWebhookNotification(session, types_1.WebhookEvent.CONNECTED, {
                    user: session.client.user,
                    phoneNumber: session.phoneNumber
                });
            }
        });
    }
    handleMessagesUpsert(session, m, logger) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            if (m.type === 'notify') {
                for (const msg of m.messages) {
                    if (!msg.key.fromMe && (0, baileys_1.isJidUser)(msg.key.remoteJid || '')) {
                        const from = ((_a = msg.key.remoteJid) === null || _a === void 0 ? void 0 : _a.split('@')[0]) || 'unknown';
                        const messageType = Object.keys(msg.message || {})[0] || 'unknown';
                        logger.messageReceived(from, messageType);
                        // Update metadata
                        yield this.updateSessionMetadata(session.id, {
                            messagesReceived: session.metadata.messagesReceived + 1
                        });
                        // Send webhook
                        yield this.sendWebhookNotification(session, types_1.WebhookEvent.MESSAGE, msg);
                    }
                }
            }
        });
    }
    sendWebhookNotification(session, event, data) {
        return __awaiter(this, void 0, void 0, function* () {
            if (!session.webhookUrl)
                return;
            const logger = (0, logger_1.createSessionLogger)(session.id, session.userId);
            try {
                const startTime = Date.now();
                yield (0, webhook_1.sendWebhook)({
                    sessionId: session.id,
                    event,
                    data,
                    timestamp: new Date().toISOString()
                }, session.webhookUrl);
                const responseTime = Date.now() - startTime;
                logger.webhookSent(session.webhookUrl, event, true, responseTime);
                // Update success count
                yield this.updateSessionMetadata(session.id, {
                    webhookCallsSuccess: session.metadata.webhookCallsSuccess + 1
                });
            }
            catch (error) {
                logger.webhookSent(session.webhookUrl, event, false);
                // Update failure count
                yield this.updateSessionMetadata(session.id, {
                    webhookCallsFailed: session.metadata.webhookCallsFailed + 1
                });
            }
        });
    }
    updateSessionMetadata(sessionId, updates) {
        return __awaiter(this, void 0, void 0, function* () {
            const session = this.sessions.get(sessionId);
            if (session) {
                session.metadata = Object.assign(Object.assign({}, session.metadata), updates);
            }
            yield dynamodb_service_1.default.updateSessionMetadata(sessionId, updates);
        });
    }
    formatPhoneNumber(phoneNumber) {
        const digits = phoneNumber.replace(/\D/g, '');
        if (digits.startsWith('0')) {
            return `6${digits.substring(1)}@s.whatsapp.net`;
        }
        else if (!digits.includes('@')) {
            return `${digits}@s.whatsapp.net`;
        }
        return phoneNumber;
    }
    checkUserSessionLimits(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            // Get max sessions from config
            const maxSessionsConfig = yield dynamodb_service_1.default.getConfig('MAX_SESSIONS_PER_USER');
            const maxSessions = maxSessionsConfig ? parseInt(maxSessionsConfig.configValue) : config_1.default.session.maxSessionsPerUser;
            // Get user's current sessions
            const { items: userSessions } = yield dynamodb_service_1.default.getSessionsByUser(userId);
            const activeSessions = userSessions.filter(s => s.status === types_1.SessionStatus.CONNECTED || s.status === types_1.SessionStatus.PENDING);
            if (activeSessions.length >= maxSessions) {
                throw new Error(`User ${userId} has reached maximum sessions limit (${maxSessions})`);
            }
        });
    }
    startSessionCleanup() {
        this.sessionCleanupInterval = setInterval(() => __awaiter(this, void 0, void 0, function* () {
            yield this.cleanupExpiredSessions();
        }), config_1.default.session.cleanupIntervalSeconds * 1000);
        logger_1.whatsappLogger.info('Session cleanup scheduler started', {
            intervalSeconds: config_1.default.session.cleanupIntervalSeconds
        });
    }
    cleanupExpiredSessions() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const now = Date.now();
                const { items: allSessions } = yield dynamodb_service_1.default.getAllSessions();
                const expiredSessions = allSessions.filter(session => session.expiresAt < now ||
                    (session.status === types_1.SessionStatus.FAILED && session.connectionAttempts >= 5));
                for (const expiredSession of expiredSessions) {
                    logger_1.whatsappLogger.info('Cleaning up expired session', {
                        sessionId: expiredSession.sessionId,
                        status: expiredSession.status,
                        expired: expiredSession.expiresAt < now
                    });
                    yield this.deleteSession(expiredSession.sessionId);
                }
                if (expiredSessions.length > 0) {
                    logger_1.whatsappLogger.info('Session cleanup completed', { cleaned: expiredSessions.length });
                }
            }
            catch (error) {
                logger_1.whatsappLogger.error('Session cleanup failed', error);
            }
        });
    }
    shutdown() {
        return __awaiter(this, void 0, void 0, function* () {
            logger_1.whatsappLogger.info('Shutting down WhatsApp service');
            this.isShuttingDown = true;
            // Stop cleanup scheduler
            if (this.sessionCleanupInterval) {
                clearInterval(this.sessionCleanupInterval);
            }
            // Close all sessions
            const closePromises = Array.from(this.sessions.values()).map((session) => __awaiter(this, void 0, void 0, function* () {
                try {
                    session.client.end(undefined);
                }
                catch (error) {
                    logger_1.whatsappLogger.error('Error closing session during shutdown', error, { sessionId: session.id });
                }
            }));
            yield Promise.all(closePromises);
            this.sessions.clear();
            logger_1.whatsappLogger.serviceStopped();
        });
    }
}
// Create singleton instance
const enhancedWhatsappService = new EnhancedWhatsAppService();
exports.default = enhancedWhatsappService;
//# sourceMappingURL=enhanced-whatsapp.service.js.map