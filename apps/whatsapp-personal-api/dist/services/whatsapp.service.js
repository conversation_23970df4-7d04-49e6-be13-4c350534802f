"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const baileys_1 = __importStar(require("@whiskeysockets/baileys"));
// import { Boom } from '@hapi/boom';
// @ts-ignore
const qrcode_terminal_1 = __importDefault(require("qrcode-terminal"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const config_1 = __importDefault(require("../config"));
const webhook_1 = require("../utils/webhook");
class WhatsAppService {
    constructor() {
        this.sessions = new Map();
        // Create sessions directory if it doesn't exist
        if (!fs_1.default.existsSync(config_1.default.sessionsDir)) {
            fs_1.default.mkdirSync(config_1.default.sessionsDir, { recursive: true });
        }
    }
    createSession(sessionId, sessionName) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Check if session already exists
                if (this.sessions.has(sessionId)) {
                    return this.sessions.get(sessionId) || null;
                }
                // Create session directory
                const sessionDir = path_1.default.join(config_1.default.sessionsDir, sessionId);
                if (!fs_1.default.existsSync(sessionDir)) {
                    fs_1.default.mkdirSync(sessionDir, { recursive: true });
                }
                // Initialize auth state
                const { state, saveCreds } = yield (0, baileys_1.useMultiFileAuthState)(sessionDir);
                // Fetch latest version
                const { version } = yield (0, baileys_1.fetchLatestBaileysVersion)();
                // Create WhatsApp client with exact settings from test script
                const sock = (0, baileys_1.default)({
                    auth: {
                        creds: state.creds,
                        // @ts-ignore
                        keys: (0, baileys_1.makeCacheableSignalKeyStore)(state.keys, console.log),
                    },
                    printQRInTerminal: true, // Enable QR code in terminal for debugging
                    browser: ['WhatsApp API Test', 'Chrome', '103.0.5060.114'], // Exact browser signature from test script
                });
                // Create session object without a QR code initially
                // The QR code will be set when Baileys generates it
                const session = {
                    id: sessionId,
                    name: sessionName,
                    client: sock,
                    ready: false,
                };
                // Store session immediately
                this.sessions.set(sessionId, session);
                // Set up connection events
                sock.ev.on('connection.update', (update) => __awaiter(this, void 0, void 0, function* () {
                    var _a, _b;
                    const { connection, lastDisconnect, qr } = update;
                    if (qr) {
                        console.log(`QR code received for session ${sessionId}`);
                        // Store the QR code in the session
                        session.qr = qr;
                        // Display QR code in terminal for debugging
                        qrcode_terminal_1.default.generate(qr, { small: true });
                        // Log the QR code for debugging
                        console.log(`Raw QR code data for session ${sessionId}:`, qr);
                        // Send webhook notification
                        yield (0, webhook_1.sendWebhook)({
                            sessionId,
                            event: 'qr',
                            data: { qr },
                        });
                        // Update the session in the map to ensure QR code is available
                        this.sessions.set(sessionId, session);
                        // Log confirmation that session was updated
                        console.log(`Session ${sessionId} updated with QR code`);
                        // Log instructions for debugging
                        console.log('To scan the QR code:');
                        console.log('1. Open WhatsApp on your phone');
                        console.log('2. Tap Menu or Settings and select WhatsApp Web/Desktop');
                        console.log('3. Point your phone at this screen to capture the QR code');
                    }
                    if (connection === 'close') {
                        const shouldReconnect = ((_b = (_a = lastDisconnect === null || lastDisconnect === void 0 ? void 0 : lastDisconnect.error) === null || _a === void 0 ? void 0 : _a.output) === null || _b === void 0 ? void 0 : _b.statusCode) !== baileys_1.DisconnectReason.loggedOut;
                        if (shouldReconnect) {
                            // Reconnect if not logged out
                            yield this.createSession(sessionId, sessionName);
                        }
                        else {
                            // Remove session if logged out
                            this.sessions.delete(sessionId);
                            // Send webhook notification
                            yield (0, webhook_1.sendWebhook)({
                                sessionId,
                                event: 'disconnected',
                                data: { reason: 'logged_out' },
                            });
                        }
                    }
                    else if (connection === 'open') {
                        // Session is ready
                        session.ready = true;
                        session.lastSeen = new Date();
                        // Send webhook notification
                        yield (0, webhook_1.sendWebhook)({
                            sessionId,
                            event: 'connected',
                            data: {
                                user: sock.user,
                                time: new Date().toISOString(),
                            },
                        });
                    }
                }));
                // Save credentials on update
                sock.ev.on('creds.update', saveCreds);
                // Handle messages
                sock.ev.on('messages.upsert', (m) => __awaiter(this, void 0, void 0, function* () {
                    if (m.type === 'notify') {
                        for (const msg of m.messages) {
                            if (!msg.key.fromMe && (0, baileys_1.isJidUser)(msg.key.remoteJid || '')) {
                                // Send webhook notification for new message
                                yield (0, webhook_1.sendWebhook)({
                                    sessionId,
                                    event: 'message',
                                    data: msg,
                                });
                            }
                        }
                    }
                }));
                // Store session
                this.sessions.set(sessionId, session);
                return session;
            }
            catch (error) {
                console.error(`Error creating session ${sessionId}:`, error);
                return null;
            }
        });
    }
    getSession(sessionId) {
        return this.sessions.get(sessionId) || null;
    }
    getAllSessions() {
        return Array.from(this.sessions.values());
    }
    deleteSession(sessionId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const session = this.sessions.get(sessionId);
                if (!session) {
                    return false;
                }
                // Close connection
                session.client.end(undefined);
                // Remove from sessions map
                this.sessions.delete(sessionId);
                // Delete session directory
                const sessionDir = path_1.default.join(config_1.default.sessionsDir, sessionId);
                if (fs_1.default.existsSync(sessionDir)) {
                    fs_1.default.rmSync(sessionDir, { recursive: true, force: true });
                }
                return true;
            }
            catch (error) {
                console.error(`Error deleting session ${sessionId}:`, error);
                return false;
            }
        });
    }
    sendTextMessage(sessionId, to, text) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const session = this.sessions.get(sessionId);
                if (!session || !session.ready) {
                    throw new Error('Session not found or not ready');
                }
                // Format phone number
                const formattedNumber = this.formatPhoneNumber(to);
                // Send message
                const result = yield session.client.sendMessage(formattedNumber, { text });
                return result;
            }
            catch (error) {
                console.error(`Error sending message in session ${sessionId}:`, error);
                throw error;
            }
        });
    }
    sendMediaMessage(sessionId, to, url, caption, mimetype) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const session = this.sessions.get(sessionId);
                if (!session || !session.ready) {
                    throw new Error('Session not found or not ready');
                }
                // Format phone number
                const formattedNumber = this.formatPhoneNumber(to);
                // Send media message
                const result = yield session.client.sendMessage(formattedNumber, {
                    image: { url },
                    caption: caption || '',
                    mimetype: mimetype || 'image/jpeg',
                });
                return result;
            }
            catch (error) {
                console.error(`Error sending media message in session ${sessionId}:`, error);
                throw error;
            }
        });
    }
    formatPhoneNumber(phoneNumber) {
        // Remove any non-digit characters
        const digits = phoneNumber.replace(/\D/g, '');
        // Ensure the number has the country code
        if (digits.startsWith('0')) {
            return `6${digits.substring(1)}@s.whatsapp.net`;
        }
        else if (!digits.includes('@')) {
            return `${digits}@s.whatsapp.net`;
        }
        return phoneNumber;
    }
}
// Create singleton instance
const whatsappService = new WhatsAppService();
exports.default = whatsappService;
//# sourceMappingURL=whatsapp.service.js.map