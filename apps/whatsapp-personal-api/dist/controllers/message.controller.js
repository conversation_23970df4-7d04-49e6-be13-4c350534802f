"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validatePhoneNumber = exports.sendMediaMessage = exports.sendTextMessage = exports.sendBulkMessages = exports.sendMessage = void 0;
const joi_1 = __importDefault(require("joi"));
const enhanced_whatsapp_service_1 = __importDefault(require("../services/enhanced-whatsapp.service"));
const logger_1 = require("../utils/logger");
const logger = (0, logger_1.createComponentLogger)('MessageController');
// Validation schemas
const mediaSchema = joi_1.default.object({
    url: joi_1.default.string().uri().required(),
    caption: joi_1.default.string().optional().max(1000),
    mimetype: joi_1.default.string().optional()
});
const sendMessageSchema = joi_1.default.object({
    to: joi_1.default.string().required().pattern(/^\+?[1-9]\d{1,14}$/),
    text: joi_1.default.string().optional().max(4096),
    media: mediaSchema.optional()
}).xor('text', 'media');
const bulkMessageSchema = joi_1.default.object({
    recipients: joi_1.default.array().items(joi_1.default.string().pattern(/^\+?[1-9]\d{1,14}$/)).min(1).max(100).required(),
    message: joi_1.default.object({
        text: joi_1.default.string().optional().max(4096),
        media: mediaSchema.optional()
    }).xor('text', 'media').required(),
    delay: joi_1.default.number().integer().min(0).max(10000).default(1000)
});
// Helper function to create API response
function createResponse(success, data, error) {
    return {
        success,
        data,
        error: error ? { code: 'API_ERROR', message: error, timestamp: new Date() } : undefined,
        timestamp: new Date()
    };
}
// ===== SINGLE MESSAGE ENDPOINTS =====
const sendMessage = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c, _d, _e;
    try {
        const { sessionId } = req.params;
        if (!sessionId) {
            res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
            return;
        }
        const { error, value } = sendMessageSchema.validate(req.body);
        if (error) {
            logger.warn('Invalid send message request', {
                sessionId,
                error: error.details[0].message
            });
            res.status(400).json(createResponse(false, undefined, error.details[0].message));
            return;
        }
        const messageRequest = value;
        let result;
        if (messageRequest.text) {
            // Send text message
            result = yield enhanced_whatsapp_service_1.default.sendTextMessage(sessionId, messageRequest.to, messageRequest.text);
            logger.info('Text message sent', {
                sessionId,
                to: `****${messageRequest.to.slice(-4)}`,
                messageId: (_a = result === null || result === void 0 ? void 0 : result.key) === null || _a === void 0 ? void 0 : _a.id
            });
        }
        else if (messageRequest.media) {
            // Send media message
            result = yield enhanced_whatsapp_service_1.default.sendMediaMessage(sessionId, messageRequest.to, messageRequest.media.url, messageRequest.media.caption, messageRequest.media.mimetype);
            logger.info('Media message sent', {
                sessionId,
                to: `****${messageRequest.to.slice(-4)}`,
                mediaType: messageRequest.media.mimetype,
                messageId: (_b = result === null || result === void 0 ? void 0 : result.key) === null || _b === void 0 ? void 0 : _b.id
            });
        }
        res.status(200).json(createResponse(true, {
            messageId: (_c = result === null || result === void 0 ? void 0 : result.key) === null || _c === void 0 ? void 0 : _c.id,
            timestamp: result === null || result === void 0 ? void 0 : result.messageTimestamp,
            status: 'sent'
        }));
    }
    catch (error) {
        logger.error('Failed to send message', error, { sessionId: req.params.sessionId });
        const message = error.message || 'Failed to send message';
        // Return appropriate status code based on error type
        const statusCode = ((_d = error.message) === null || _d === void 0 ? void 0 : _d.includes('not found')) ? 404 :
            ((_e = error.message) === null || _e === void 0 ? void 0 : _e.includes('not ready')) ? 409 : 500;
        res.status(statusCode).json(createResponse(false, undefined, message));
    }
});
exports.sendMessage = sendMessage;
// ===== BULK MESSAGING ENDPOINTS =====
const sendBulkMessages = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { sessionId } = req.params;
        if (!sessionId) {
            res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
            return;
        }
        const { error, value } = bulkMessageSchema.validate(req.body);
        if (error) {
            logger.warn('Invalid bulk message request', {
                sessionId,
                error: error.details[0].message
            });
            res.status(400).json(createResponse(false, undefined, error.details[0].message));
            return;
        }
        const bulkRequest = value;
        logger.info('Starting bulk message operation', {
            sessionId,
            recipientCount: bulkRequest.recipients.length,
            delay: bulkRequest.delay
        });
        const result = yield enhanced_whatsapp_service_1.default.sendBulkMessages(sessionId, bulkRequest);
        logger.info('Bulk message operation completed', {
            sessionId,
            total: result.summary.total,
            sent: result.summary.sent,
            failed: result.summary.failed,
            successRate: result.summary.sent / result.summary.total * 100
        });
        res.status(200).json(createResponse(true, result));
    }
    catch (error) {
        logger.error('Failed to send bulk messages', error, { sessionId: req.params.sessionId });
        const message = error.message || 'Failed to send bulk messages';
        const statusCode = ((_a = error.message) === null || _a === void 0 ? void 0 : _a.includes('not found')) ? 404 :
            ((_b = error.message) === null || _b === void 0 ? void 0 : _b.includes('not ready')) ? 409 : 500;
        res.status(statusCode).json(createResponse(false, undefined, message));
    }
});
exports.sendBulkMessages = sendBulkMessages;
// ===== LEGACY ENDPOINTS (for backward compatibility) =====
const sendTextMessage = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { sessionId } = req.params;
        const { to, text } = req.body;
        if (!sessionId || !to || !text) {
            res.status(400).json(createResponse(false, undefined, 'Session ID, recipient, and text are required'));
            return;
        }
        const result = yield enhanced_whatsapp_service_1.default.sendTextMessage(sessionId, to, text);
        logger.info('Legacy text message sent', {
            sessionId,
            to: `****${to.slice(-4)}`,
            messageId: (_a = result === null || result === void 0 ? void 0 : result.key) === null || _a === void 0 ? void 0 : _a.id
        });
        res.status(200).json(createResponse(true, {
            messageId: (_b = result === null || result === void 0 ? void 0 : result.key) === null || _b === void 0 ? void 0 : _b.id,
            timestamp: result === null || result === void 0 ? void 0 : result.messageTimestamp,
            status: 'sent'
        }));
    }
    catch (error) {
        logger.error('Failed to send text message (legacy)', error, { sessionId: req.params.sessionId });
        const message = error.message || 'Failed to send text message';
        res.status(500).json(createResponse(false, undefined, message));
    }
});
exports.sendTextMessage = sendTextMessage;
const sendMediaMessage = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { sessionId } = req.params;
        const { to, url, caption, mimetype } = req.body;
        if (!sessionId || !to || !url) {
            res.status(400).json(createResponse(false, undefined, 'Session ID, recipient, and media URL are required'));
            return;
        }
        const result = yield enhanced_whatsapp_service_1.default.sendMediaMessage(sessionId, to, url, caption, mimetype);
        logger.info('Legacy media message sent', {
            sessionId,
            to: `****${to.slice(-4)}`,
            mediaType: mimetype,
            messageId: (_a = result === null || result === void 0 ? void 0 : result.key) === null || _a === void 0 ? void 0 : _a.id
        });
        res.status(200).json(createResponse(true, {
            messageId: (_b = result === null || result === void 0 ? void 0 : result.key) === null || _b === void 0 ? void 0 : _b.id,
            timestamp: result === null || result === void 0 ? void 0 : result.messageTimestamp,
            status: 'sent'
        }));
    }
    catch (error) {
        logger.error('Failed to send media message (legacy)', error, { sessionId: req.params.sessionId });
        const message = error.message || 'Failed to send media message';
        res.status(500).json(createResponse(false, undefined, message));
    }
});
exports.sendMediaMessage = sendMediaMessage;
// ===== MESSAGE VALIDATION ENDPOINTS =====
const validatePhoneNumber = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { phoneNumber } = req.body;
        if (!phoneNumber) {
            res.status(400).json(createResponse(false, undefined, 'Phone number is required'));
            return;
        }
        // Basic phone number validation
        const phoneRegex = /^\+?[1-9]\d{1,14}$/;
        const isValid = phoneRegex.test(phoneNumber);
        const response = {
            phoneNumber,
            isValid,
            formatted: isValid ? phoneNumber.replace(/\D/g, '') : null,
            countryCode: null // Could be enhanced with phone number parsing library
        };
        res.status(200).json(createResponse(true, response));
    }
    catch (error) {
        logger.error('Failed to validate phone number', error);
        res.status(500).json(createResponse(false, undefined, 'Failed to validate phone number'));
    }
});
exports.validatePhoneNumber = validatePhoneNumber;
//# sourceMappingURL=message.controller.js.map