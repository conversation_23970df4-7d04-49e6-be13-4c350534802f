"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllSessionsHealth = exports.getDetailedMetrics = exports.getServiceHealth = exports.getUserSessions = exports.getSessionStats = exports.getSessionHealth = exports.reconnectSession = exports.deleteSession = exports.updateSession = exports.getAllSessions = exports.getSession = exports.createSession = void 0;
const joi_1 = __importDefault(require("joi"));
const enhanced_whatsapp_service_1 = __importDefault(require("../services/enhanced-whatsapp.service"));
const monitoring_service_1 = __importDefault(require("../services/monitoring.service"));
const dynamodb_service_1 = __importDefault(require("../services/dynamodb.service"));
const logger_1 = require("../utils/logger");
const logger = (0, logger_1.createComponentLogger)('SessionController');
// Validation schemas
const createSessionSchema = joi_1.default.object({
    id: joi_1.default.string().required().min(1).max(50).pattern(/^[a-zA-Z0-9_-]+$/),
    userId: joi_1.default.string().required().min(1).max(100),
    phoneNumber: joi_1.default.string().optional().pattern(/^\+?[1-9]\d{1,14}$/),
    name: joi_1.default.string().optional().max(100),
    webhookUrl: joi_1.default.string().uri().optional()
});
const updateSessionSchema = joi_1.default.object({
    name: joi_1.default.string().optional().max(100),
    webhookUrl: joi_1.default.string().uri().optional().allow('')
});
const paginationSchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    limit: joi_1.default.number().integer().min(1).max(100).default(10),
    sortBy: joi_1.default.string().optional(),
    sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc')
});
// Helper function to create API response
function createResponse(success, data, error) {
    return {
        success,
        data,
        error: error ? { code: 'API_ERROR', message: error, timestamp: new Date() } : undefined,
        timestamp: new Date()
    };
}
// ===== SESSION MANAGEMENT ENDPOINTS =====
const createSession = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { error, value } = createSessionSchema.validate(req.body);
        if (error) {
            logger.warn('Invalid create session request', { error: error.details[0].message });
            res.status(400).json(createResponse(false, undefined, error.details[0].message));
            return;
        }
        const sessionRequest = value;
        const session = yield enhanced_whatsapp_service_1.default.createSession(sessionRequest);
        logger.info('Session created successfully', {
            sessionId: session.id,
            userId: session.userId
        });
        const sessionInfo = {
            id: session.id,
            name: session.name,
            userId: session.userId,
            ready: session.ready,
            status: session.status,
            phoneNumber: session.phoneNumber,
            webhookUrl: session.webhookUrl,
            createdAt: session.createdAt,
            lastSeen: session.lastSeen,
            connectionAttempts: session.connectionAttempts,
            metadata: session.metadata
        };
        // Include QR code in response for new sessions
        const responseData = Object.assign(Object.assign({}, sessionInfo), { qr: session.qr });
        res.status(201).json(createResponse(true, responseData));
    }
    catch (error) {
        logger.error('Failed to create session', error);
        const message = error.message || 'Failed to create session';
        res.status(500).json(createResponse(false, undefined, message));
    }
});
exports.createSession = createSession;
const getSession = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!id) {
            res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
            return;
        }
        const session = yield enhanced_whatsapp_service_1.default.getSession(id);
        if (!session) {
            res.status(404).json(createResponse(false, undefined, 'Session not found'));
            return;
        }
        const sessionInfo = {
            id: session.id,
            name: session.name,
            userId: session.userId,
            ready: session.ready,
            status: session.status,
            phoneNumber: session.phoneNumber,
            webhookUrl: session.webhookUrl,
            createdAt: session.createdAt,
            lastSeen: session.lastSeen,
            connectionAttempts: session.connectionAttempts,
            metadata: session.metadata
        };
        // Include QR code if session is pending
        const responseData = Object.assign(Object.assign({}, sessionInfo), (session.qr && { qr: session.qr }));
        res.status(200).json(createResponse(true, responseData));
    }
    catch (error) {
        logger.error('Failed to get session', error, { sessionId: req.params.id });
        res.status(500).json(createResponse(false, undefined, 'Failed to get session'));
    }
});
exports.getSession = getSession;
const getAllSessions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { error, value } = paginationSchema.validate(req.query);
        if (error) {
            res.status(400).json(createResponse(false, undefined, error.details[0].message));
            return;
        }
        const options = value;
        const sessions = enhanced_whatsapp_service_1.default.getAllSessions();
        // Apply pagination
        const startIndex = (options.page - 1) * options.limit;
        const endIndex = startIndex + options.limit;
        const paginatedSessions = sessions.slice(startIndex, endIndex);
        const sessionInfos = paginatedSessions.map(session => ({
            id: session.id,
            name: session.name,
            userId: session.userId,
            ready: session.ready,
            status: session.status,
            phoneNumber: session.phoneNumber,
            webhookUrl: session.webhookUrl,
            createdAt: session.createdAt,
            lastSeen: session.lastSeen,
            connectionAttempts: session.connectionAttempts,
            metadata: session.metadata
        }));
        const totalPages = Math.ceil(sessions.length / options.limit);
        const response = {
            items: sessionInfos,
            pagination: {
                page: options.page,
                limit: options.limit,
                total: sessions.length,
                totalPages,
                hasNext: options.page < totalPages,
                hasPrev: options.page > 1
            }
        };
        res.status(200).json(createResponse(true, response));
    }
    catch (error) {
        logger.error('Failed to get all sessions', error);
        res.status(500).json(createResponse(false, undefined, 'Failed to get sessions'));
    }
});
exports.getAllSessions = getAllSessions;
const updateSession = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { error, value } = updateSessionSchema.validate(req.body);
        if (error) {
            res.status(400).json(createResponse(false, undefined, error.details[0].message));
            return;
        }
        if (!id) {
            res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
            return;
        }
        const updates = value;
        const session = yield enhanced_whatsapp_service_1.default.updateSession(id, updates);
        logger.info('Session updated successfully', {
            sessionId: id,
            updates: Object.keys(updates)
        });
        const sessionInfo = {
            id: session.id,
            name: session.name,
            userId: session.userId,
            ready: session.ready,
            status: session.status,
            phoneNumber: session.phoneNumber,
            webhookUrl: session.webhookUrl,
            createdAt: session.createdAt,
            lastSeen: session.lastSeen,
            connectionAttempts: session.connectionAttempts,
            metadata: session.metadata
        };
        res.status(200).json(createResponse(true, sessionInfo));
    }
    catch (error) {
        logger.error('Failed to update session', error, { sessionId: req.params.id });
        const message = error.message || 'Failed to update session';
        res.status(500).json(createResponse(false, undefined, message));
    }
});
exports.updateSession = updateSession;
const deleteSession = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!id) {
            res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
            return;
        }
        const success = yield enhanced_whatsapp_service_1.default.deleteSession(id);
        if (!success) {
            res.status(404).json(createResponse(false, undefined, 'Session not found'));
            return;
        }
        logger.info('Session deleted successfully', { sessionId: id });
        res.status(200).json(createResponse(true, { message: 'Session deleted successfully' }));
    }
    catch (error) {
        logger.error('Failed to delete session', error, { sessionId: req.params.id });
        res.status(500).json(createResponse(false, undefined, 'Failed to delete session'));
    }
});
exports.deleteSession = deleteSession;
const reconnectSession = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!id) {
            res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
            return;
        }
        const session = yield enhanced_whatsapp_service_1.default.reconnectSession(id);
        logger.info('Session reconnection initiated', { sessionId: id });
        const sessionInfo = {
            id: session.id,
            name: session.name,
            userId: session.userId,
            ready: session.ready,
            status: session.status,
            phoneNumber: session.phoneNumber,
            webhookUrl: session.webhookUrl,
            createdAt: session.createdAt,
            lastSeen: session.lastSeen,
            connectionAttempts: session.connectionAttempts,
            metadata: session.metadata
        };
        res.status(200).json(createResponse(true, sessionInfo));
    }
    catch (error) {
        logger.error('Failed to reconnect session', error, { sessionId: req.params.id });
        const message = error.message || 'Failed to reconnect session';
        res.status(500).json(createResponse(false, undefined, message));
    }
});
exports.reconnectSession = reconnectSession;
// ===== SESSION MONITORING ENDPOINTS =====
const getSessionHealth = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!id) {
            res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
            return;
        }
        const health = yield enhanced_whatsapp_service_1.default.getSessionHealth(id);
        res.status(200).json(createResponse(true, health));
    }
    catch (error) {
        logger.error('Failed to get session health', error, { sessionId: req.params.id });
        const message = error.message || 'Failed to get session health';
        res.status(500).json(createResponse(false, undefined, message));
    }
});
exports.getSessionHealth = getSessionHealth;
const getSessionStats = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!id) {
            res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
            return;
        }
        const stats = yield enhanced_whatsapp_service_1.default.getSessionStats(id);
        res.status(200).json(createResponse(true, stats));
    }
    catch (error) {
        logger.error('Failed to get session stats', error, { sessionId: req.params.id });
        const message = error.message || 'Failed to get session stats';
        res.status(500).json(createResponse(false, undefined, message));
    }
});
exports.getSessionStats = getSessionStats;
// ===== USER SESSION MANAGEMENT =====
const getUserSessions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId } = req.params;
        const { error, value } = paginationSchema.validate(req.query);
        if (error) {
            res.status(400).json(createResponse(false, undefined, error.details[0].message));
            return;
        }
        if (!userId) {
            res.status(400).json(createResponse(false, undefined, 'User ID is required'));
            return;
        }
        const options = value;
        const result = yield dynamodb_service_1.default.getSessionsByUser(userId, options);
        const sessionInfos = result.items.map(record => ({
            id: record.sessionId,
            name: record.sessionName,
            userId: record.userId,
            ready: false, // Would need to check in-memory sessions
            status: record.status,
            phoneNumber: record.phoneNumber,
            webhookUrl: record.webhookUrl,
            createdAt: new Date(record.createdAt),
            lastSeen: record.lastSeen ? new Date(record.lastSeen) : undefined,
            connectionAttempts: record.connectionAttempts,
            metadata: record.metadata
        }));
        const response = {
            items: sessionInfos,
            pagination: result.pagination
        };
        res.status(200).json(createResponse(true, response));
    }
    catch (error) {
        logger.error('Failed to get user sessions', error, { userId: req.params.userId });
        res.status(500).json(createResponse(false, undefined, 'Failed to get user sessions'));
    }
});
exports.getUserSessions = getUserSessions;
// ===== SERVICE MONITORING ENDPOINTS =====
const getServiceHealth = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const health = yield monitoring_service_1.default.getServiceHealth();
        res.status(200).json(createResponse(true, health));
    }
    catch (error) {
        logger.error('Failed to get service health', error);
        res.status(500).json(createResponse(false, undefined, 'Failed to get service health'));
    }
});
exports.getServiceHealth = getServiceHealth;
const getDetailedMetrics = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const metrics = yield monitoring_service_1.default.getDetailedServiceMetrics();
        res.status(200).json(createResponse(true, metrics));
    }
    catch (error) {
        logger.error('Failed to get detailed metrics', error);
        res.status(500).json(createResponse(false, undefined, 'Failed to get detailed metrics'));
    }
});
exports.getDetailedMetrics = getDetailedMetrics;
const getAllSessionsHealth = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const healthData = yield monitoring_service_1.default.getAllSessionsHealth();
        res.status(200).json(createResponse(true, healthData));
    }
    catch (error) {
        logger.error('Failed to get all sessions health', error);
        res.status(500).json(createResponse(false, undefined, 'Failed to get sessions health'));
    }
});
exports.getAllSessionsHealth = getAllSessionsHealth;
//# sourceMappingURL=session.controller.js.map