version: '3.8'

services:
  # DynamoDB Local for development
  dynamodb-local:
    image: amazon/dynamodb-local:latest
    container_name: whatsapp-dynamodb-local
    ports:
      - "8000:8000"
    command: ["-jar", "DynamoDBLocal.jar", "-sharedDb", "-optimizeDbBeforeStartup"]
    volumes:
      - dynamodb-data:/home/<USER>/data
    working_dir: /home/<USER>
    environment:
      - AWS_ACCESS_KEY_ID=dummy
      - AWS_SECRET_ACCESS_KEY=dummy
      - AWS_DEFAULT_REGION=ap-southeast-1
    networks:
      - whatsapp-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # DynamoDB Admin UI (optional - for easy table management)
  dynamodb-admin:
    image: aaronshaf/dynamodb-admin:latest
    container_name: whatsapp-dynamodb-admin
    ports:
      - "8001:8001"
    environment:
      - DYNAMO_ENDPOINT=http://dynamodb-local:8000
      - AWS_REGION=ap-southeast-1
      - AWS_ACCESS_KEY_ID=dummy
      - AWS_SECRET_ACCESS_KEY=dummy
    depends_on:
      dynamodb-local:
        condition: service_healthy
    networks:
      - whatsapp-network

  # WhatsApp API Service
  whatsapp-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "${PORT:-3030}:${PORT:-3030}"
    volumes:
      - ./whatsapp-session:/app/whatsapp-session  # Volume for WhatsApp session data
      - whatsapp_node_modules:/app/node_modules  # Volume for node_modules to preserve patched files
      - ./logs:/app/logs  # Volume for logs
    env_file:
      - .env
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=${PORT:-3030}
      - HOST=${HOST:-0.0.0.0}
      - WEBHOOK_URL=${WEBHOOK_URL:-}
      - WEBHOOK_ENABLED=${WEBHOOK_ENABLED:-false}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET:-}
      # DynamoDB Local configuration
      - DYNAMODB_ENDPOINT=http://dynamodb-local:8000
      - AWS_ACCESS_KEY_ID=dummy
      - AWS_SECRET_ACCESS_KEY=dummy
      - AWS_REGION=ap-southeast-1
    depends_on:
      dynamodb-local:
        condition: service_healthy
    networks:
      - whatsapp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-3030}/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  whatsapp_node_modules:  # Named volume for node_modules
  dynamodb-data:  # Named volume for DynamoDB Local data persistence

networks:
  whatsapp-network:
    driver: bridge
