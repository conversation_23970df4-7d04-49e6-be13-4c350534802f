import express from 'express';
import cors from 'cors';
import path from 'path';
import fs from 'fs';
import config from './config';
console.log('Config loaded successfully:', config.port);
// Commenting out imports to isolate the issue
import enhancedWhatsappService from './services/enhanced-whatsapp.service';
console.log('Enhanced WhatsApp service loaded');
import monitoringService from './services/monitoring.service';
console.log('Monitoring service loaded');
import dynamoDBService from './services/dynamodb.service';
console.log('DynamoDB service loaded');
import { createComponentLogger } from './utils/logger';

const logger = createComponentLogger('Application');

// Create Express app
const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Simple root endpoint
app.get('/', (req, res) => {
  res.json({
    service: 'WhatsApp Manager API',
    version: '2.0.0',
    status: 'running (debug mode)',
    timestamp: new Date().toISOString()
  });
});

// Start server
async function startServer() {
  try {
    logger.info('Starting WhatsApp Manager Service', {
      version: '2.0.0',
      environment: config.environment,
      port: config.port
    });

    // Test DynamoDB connection
    const dynamoHealthy = await dynamoDBService.healthCheck();
    if (!dynamoHealthy) {
      logger.error('DynamoDB health check failed');
      process.exit(1);
    }
    logger.info('DynamoDB connection verified');

    const server = app.listen(config.port, () => {
      logger.info('WhatsApp Manager Service started successfully', {
        port: config.port,
        environment: config.environment,
        pid: process.pid,
        nodeVersion: process.version
      });
    });
  } catch (error: any) {
    logger.error('Failed to start server', error);
    process.exit(1);
  }
}

startServer();
