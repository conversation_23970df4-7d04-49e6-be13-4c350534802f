import express from 'express';
import cors from 'cors';

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    service: 'WhatsApp Manager API',
    version: '2.0.0',
    status: 'running',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString(),
    message: 'Server is working! Enhanced WhatsApp API is ready.'
  });
});

// Status endpoint
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    version: '2.0.0',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date()
  });
});

// Basic sessions endpoint (placeholder)
app.get('/api/sessions', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: 'Enhanced multi-session API is ready. Create sessions via POST /api/sessions'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Endpoint ${req.method} ${req.originalUrl} not found`,
      availableEndpoints: [
        'GET /',
        'GET /api/status', 
        'GET /health',
        'GET /api/sessions'
      ]
    }
  });
});

// Start server
app.listen(port, () => {
  console.log(`🚀 WhatsApp Manager API v2.0.0 is running!`);
  console.log(`📡 Server: http://localhost:${port}`);
  console.log(`🏥 Health: http://localhost:${port}/health`);
  console.log(`📋 Status: http://localhost:${port}/api/status`);
  console.log(`📱 Sessions: http://localhost:${port}/api/sessions`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});