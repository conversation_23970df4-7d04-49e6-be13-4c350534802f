import express from 'express';
import cors from 'cors';
import config from './config';
// import dynamoDBService from './services/dynamodb.service';
import { createComponentLogger } from './utils/logger';

const logger = createComponentLogger('Application');

// Create Express app
const app = express();

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('HTTP Request', {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.get('user-agent')
    });
  });
  next();
});

// CORS middleware
app.use(cors({
  origin: config.environment === 'production' ? false : true,
  credentials: true,
  optionsSuccessStatus: 200
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    service: 'WhatsApp Manager API',
    version: '2.0.0',
    status: 'running',
    environment: config.environment,
    endpoints: {
      sessions: '/api/sessions',
      health: '/health',
      status: '/api/status'
    },
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    // const isDynamoHealthy = await dynamoDBService.healthCheck();
    const health = {
      status: 'healthy',
      services: {
        api: 'running'
      },
      timestamp: new Date()
    };

    res.status(200).json({
      success: true,
      data: health,
      timestamp: new Date()
    });
  } catch (error: any) {
    logger.error('Health check failed', error);
    res.status(503).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Service health check failed',
        timestamp: new Date()
      },
      timestamp: new Date()
    });
  }
});

// Basic API status endpoint
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    status: 'running',
    version: '2.0.0',
    environment: config.environment,
    timestamp: new Date().toISOString()
  });
});

// Sessions endpoints with DynamoDB integration
app.get('/api/sessions', async (req, res) => {
  try {
    const { page, limit } = req.query;
    const result = await dynamoDBService.getAllSessions({
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined
    });
    
    res.json({
      success: true,
      data: result.items,
      pagination: result.pagination,
      timestamp: new Date()
    });
  } catch (error: any) {
    logger.error('Failed to get sessions', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve sessions',
        timestamp: new Date()
      },
      timestamp: new Date()
    });
  }
});

// Get specific session
app.get('/api/sessions/:sessionId', async (req: any, res: any) => {
  try {
    const { sessionId } = req.params;
    const session = await dynamoDBService.getSession(sessionId);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Session not found',
          timestamp: new Date()
        },
        timestamp: new Date()
      });
    }
    
    res.json({
      success: true,
      data: session,
      timestamp: new Date()
    });
  } catch (error: any) {
    logger.error('Failed to get session', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DATABASE_ERROR',
        message: 'Failed to retrieve session',
        timestamp: new Date()
      },
      timestamp: new Date()
    });
  }
});

// 404 handler
app.all('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Endpoint ${req.method} ${req.originalUrl} not found`,
      timestamp: new Date()
    },
    timestamp: new Date()
  });
});

// Global error handler
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error', error, {
    method: req.method,
    url: req.url,
    headers: req.headers
  });

  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: config.environment === 'development' ? error.message : 'Internal server error',
      timestamp: new Date()
    },
    timestamp: new Date()
  });
});

// Initialize services and start server
async function startServer() {
  try {
    logger.info('Starting WhatsApp Manager Service', {
      version: '2.0.0',
      environment: config.environment,
      port: config.port
    });

    // Test DynamoDB connection
    const dynamoHealthy = await dynamoDBService.healthCheck();
    if (!dynamoHealthy) {
      logger.error('DynamoDB health check failed');
      process.exit(1);
    }
    logger.info('DynamoDB connection verified');

    // Start server
    const server = app.listen(config.port, () => {
      logger.info('WhatsApp Manager Service started successfully', {
        port: config.port,
        environment: config.environment,
        pid: process.pid,
        nodeVersion: process.version
      });
    });

    // Graceful shutdown handling
    const shutdown = async (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown`);
      
      server.close(async () => {
        try {
          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error: any) {
          logger.error('Error during shutdown', error);
          process.exit(1);
        }
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Force shutdown after timeout');
        process.exit(1);
      }, 30000);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
      process.exit(1);
    });

  } catch (error: any) {
    logger.error('Failed to start server', error);
    process.exit(1);
  }
}

// Start the application
startServer();
