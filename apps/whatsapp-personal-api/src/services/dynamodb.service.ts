import { 
  DynamoDBClient,
  CreateTableCommand,
  DescribeTableCommand,
  ResourceNotFoundException
} from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  UpdateCommand,
  DeleteCommand,
  ScanCommand,
  QueryCommand
} from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';
import config from '../config';
import { 
  SessionRecord, 
  SessionStatus, 
  SessionMetadata, 
  PaginationOptions, 
  PaginatedResult 
} from '../types';
import { createComponentLogger } from '../utils/logger';

const logger = createComponentLogger('DynamoDBService');

class DynamoDBService {
  private client: DynamoDBClient;
  private docClient: DynamoDBDocumentClient;

  constructor() {
    // Initialize DynamoDB client with SSO support
    this.client = new DynamoDBClient({
      region: config.aws.region,
      ...(config.aws.endpoint && { endpoint: config.aws.endpoint })
    });
    
    this.docClient = DynamoDBDocumentClient.from(this.client);
    
    // Initialize tables
    this.initializeTables().catch(error => {
      logger.error('Failed to initialize DynamoDB tables', error);
    });
  }

  private async initializeTables(): Promise<void> {
    await Promise.all([
      this.createSessionTableIfNotExists(),
      this.createAuthStateTableIfNotExists(),
      this.createConfigTableIfNotExists(),
      this.createStatsTableIfNotExists()
    ]);
  }

  private async createSessionTableIfNotExists(): Promise<void> {
    try {
      await this.client.send(new DescribeTableCommand({
        TableName: config.dynamodb.sessionTableName
      }));
      logger.info('Session table already exists', { tableName: config.dynamodb.sessionTableName });
    } catch (error) {
      if (error instanceof ResourceNotFoundException) {
        logger.info('Creating session table', { tableName: config.dynamodb.sessionTableName });
        
        await this.client.send(new CreateTableCommand({
          TableName: config.dynamodb.sessionTableName,
          KeySchema: [
            { AttributeName: 'sessionId', KeyType: 'HASH' }
          ],
          AttributeDefinitions: [
            { AttributeName: 'sessionId', AttributeType: 'S' },
            { AttributeName: 'userId', AttributeType: 'S' },
            { AttributeName: 'status', AttributeType: 'S' }
          ],
          GlobalSecondaryIndexes: [
            {
              IndexName: 'UserIndex',
              KeySchema: [
                { AttributeName: 'userId', KeyType: 'HASH' },
                { AttributeName: 'sessionId', KeyType: 'RANGE' }
              ],
              Projection: { ProjectionType: 'ALL' }
            },
            {
              IndexName: 'StatusIndex',
              KeySchema: [
                { AttributeName: 'status', KeyType: 'HASH' },
                { AttributeName: 'sessionId', KeyType: 'RANGE' }
              ],
              Projection: { ProjectionType: 'ALL' }
            }
          ],
          BillingMode: 'PAY_PER_REQUEST',
          StreamSpecification: {
            StreamEnabled: true,
            StreamViewType: 'NEW_AND_OLD_IMAGES'
          }
        }));
        
        logger.info('Session table created successfully', { tableName: config.dynamodb.sessionTableName });
      } else {
        throw error;
      }
    }
  }

  private async createAuthStateTableIfNotExists(): Promise<void> {
    try {
      await this.client.send(new DescribeTableCommand({
        TableName: config.dynamodb.authStateTableName
      }));
      logger.info('Auth state table already exists', { tableName: config.dynamodb.authStateTableName });
    } catch (error) {
      if (error instanceof ResourceNotFoundException) {
        logger.info('Creating auth state table', { tableName: config.dynamodb.authStateTableName });
        
        await this.client.send(new CreateTableCommand({
          TableName: config.dynamodb.authStateTableName,
          KeySchema: [
            { AttributeName: 'sessionId', KeyType: 'HASH' },
            { AttributeName: 'keyType', KeyType: 'RANGE' }
          ],
          AttributeDefinitions: [
            { AttributeName: 'sessionId', AttributeType: 'S' },
            { AttributeName: 'keyType', AttributeType: 'S' }
          ],
          BillingMode: 'PAY_PER_REQUEST'
        }));
        
        logger.info('Auth state table created successfully', { tableName: config.dynamodb.authStateTableName });
      } else {
        throw error;
      }
    }
  }

  private async createConfigTableIfNotExists(): Promise<void> {
    try {
      await this.client.send(new DescribeTableCommand({
        TableName: config.dynamodb.configTableName
      }));
      logger.info('Config table already exists', { tableName: config.dynamodb.configTableName });
    } catch (error) {
      if (error instanceof ResourceNotFoundException) {
        logger.info('Creating config table', { tableName: config.dynamodb.configTableName });
        
        await this.client.send(new CreateTableCommand({
          TableName: config.dynamodb.configTableName,
          KeySchema: [
            { AttributeName: 'configKey', KeyType: 'HASH' }
          ],
          AttributeDefinitions: [
            { AttributeName: 'configKey', AttributeType: 'S' }
          ],
          BillingMode: 'PAY_PER_REQUEST'
        }));
        
        logger.info('Config table created successfully', { tableName: config.dynamodb.configTableName });
      } else {
        throw error;
      }
    }
  }

  private async createStatsTableIfNotExists(): Promise<void> {
    try {
      await this.client.send(new DescribeTableCommand({
        TableName: config.dynamodb.statsTableName
      }));
      logger.info('Stats table already exists', { tableName: config.dynamodb.statsTableName });
    } catch (error) {
      if (error instanceof ResourceNotFoundException) {
        logger.info('Creating stats table', { tableName: config.dynamodb.statsTableName });
        
        await this.client.send(new CreateTableCommand({
          TableName: config.dynamodb.statsTableName,
          KeySchema: [
            { AttributeName: 'sessionId', KeyType: 'HASH' },
            { AttributeName: 'date', KeyType: 'RANGE' }
          ],
          AttributeDefinitions: [
            { AttributeName: 'sessionId', AttributeType: 'S' },
            { AttributeName: 'date', AttributeType: 'S' }
          ],
          BillingMode: 'PAY_PER_REQUEST'
        }));
        
        logger.info('Stats table created successfully', { tableName: config.dynamodb.statsTableName });
      } else {
        throw error;
      }
    }
  }

  // Session Management Methods

  async createSession(sessionData: {
    sessionId: string;
    userId: string;
    sessionName?: string;
    phoneNumber?: string;
    status: SessionStatus;
    webhookUrl?: string;
    expiresAt: number;
    connectionAttempts: number;
    metadata: SessionMetadata;
  }): Promise<SessionRecord> {
    const now = Date.now();
    const record: SessionRecord = {
      ...sessionData,
      createdAt: now,
      updatedAt: now
    };

    await this.docClient.send(new PutCommand({
      TableName: config.dynamodb.sessionTableName,
      Item: record,
      ConditionExpression: 'attribute_not_exists(sessionId)'
    }));

    logger.info('Session created in DynamoDB', { sessionId: sessionData.sessionId, userId: sessionData.userId });
    return record;
  }

  async getSession(sessionId: string): Promise<SessionRecord | null> {
    const result = await this.docClient.send(new GetCommand({
      TableName: config.dynamodb.sessionTableName,
      Key: { sessionId }
    }));

    return result.Item as SessionRecord || null;
  }

  async updateSession(sessionId: string, updates: Partial<SessionRecord>): Promise<void> {
    const updateExpression: string[] = [];
    const expressionAttributeNames: Record<string, string> = {};
    const expressionAttributeValues: Record<string, any> = {};

    // Always update the updatedAt timestamp
    updates.updatedAt = Date.now();

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateExpression.push(`#${key} = :${key}`);
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributeValues[`:${key}`] = value;
      }
    });

    if (updateExpression.length === 0) {
      return;
    }

    await this.docClient.send(new UpdateCommand({
      TableName: config.dynamodb.sessionTableName,
      Key: { sessionId },
      UpdateExpression: `SET ${updateExpression.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues
    }));

    logger.debug('Session updated in DynamoDB', { sessionId, updates: Object.keys(updates) });
  }

  async deleteSession(sessionId: string): Promise<void> {
    await this.docClient.send(new DeleteCommand({
      TableName: config.dynamodb.sessionTableName,
      Key: { sessionId }
    }));

    logger.info('Session deleted from DynamoDB', { sessionId });
  }

  async getAllSessions(options: PaginationOptions = {}): Promise<PaginatedResult<SessionRecord>> {
    const { page = 1, limit = 10 } = options;
    
    const result = await this.docClient.send(new ScanCommand({
      TableName: config.dynamodb.sessionTableName,
      Limit: limit * page // Simple pagination for scan
    }));

    const items = (result.Items as SessionRecord[]) || [];
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      items: paginatedItems,
      pagination: {
        page,
        limit,
        total: items.length,
        totalPages: Math.ceil(items.length / limit),
        hasNext: endIndex < items.length,
        hasPrev: page > 1
      }
    };
  }

  async getSessionsByUser(userId: string, options: PaginationOptions = {}): Promise<PaginatedResult<SessionRecord>> {
    const { page = 1, limit = 10 } = options;
    
    const result = await this.docClient.send(new QueryCommand({
      TableName: config.dynamodb.sessionTableName,
      IndexName: 'UserIndex',
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':userId': userId
      },
      Limit: limit,
      ScanIndexForward: false // Most recent first
    }));

    const items = (result.Items as SessionRecord[]) || [];

    return {
      items,
      pagination: {
        page,
        limit,
        total: items.length,
        totalPages: Math.ceil(items.length / limit),
        hasNext: !!result.LastEvaluatedKey,
        hasPrev: page > 1
      }
    };
  }

  async getSessionsByStatus(status: SessionStatus): Promise<SessionRecord[]> {
    const result = await this.docClient.send(new QueryCommand({
      TableName: config.dynamodb.sessionTableName,
      IndexName: 'StatusIndex',
      KeyConditionExpression: 'status = :status',
      ExpressionAttributeValues: {
        ':status': status
      }
    }));

    return (result.Items as SessionRecord[]) || [];
  }

  async updateSessionMetadata(sessionId: string, metadata: Partial<SessionMetadata>): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const updatedMetadata = { ...session.metadata, ...metadata };
    await this.updateSession(sessionId, { metadata: updatedMetadata });
  }

  // Config Management Methods

  async getConfig(key: string): Promise<{ configKey: string; configValue: string } | null> {
    const result = await this.docClient.send(new GetCommand({
      TableName: config.dynamodb.configTableName,
      Key: { configKey: key }
    }));

    return result.Item as { configKey: string; configValue: string } || null;
  }

  async setConfig(key: string, value: string): Promise<void> {
    await this.docClient.send(new PutCommand({
      TableName: config.dynamodb.configTableName,
      Item: {
        configKey: key,
        configValue: value,
        updatedAt: Date.now()
      }
    }));
  }

  // Stats Management Methods

  async recordSessionStats(sessionId: string, date: string, stats: any): Promise<void> {
    await this.docClient.send(new PutCommand({
      TableName: config.dynamodb.statsTableName,
      Item: {
        sessionId,
        date,
        ...stats,
        updatedAt: Date.now()
      }
    }));
  }

  async getSessionStats(sessionId: string, startDate: string, endDate: string): Promise<any[]> {
    const result = await this.docClient.send(new QueryCommand({
      TableName: config.dynamodb.statsTableName,
      KeyConditionExpression: 'sessionId = :sessionId AND #date BETWEEN :startDate AND :endDate',
      ExpressionAttributeNames: {
        '#date': 'date'
      },
      ExpressionAttributeValues: {
        ':sessionId': sessionId,
        ':startDate': startDate,
        ':endDate': endDate
      }
    }));

    return result.Items || [];
  }

  // Health Check

  async healthCheck(): Promise<boolean> {
    try {
      await this.docClient.send(new GetCommand({
        TableName: config.dynamodb.sessionTableName,
        Key: { sessionId: 'health-check-' + Date.now() }
      }));
      return true;
    } catch (error) {
      logger.error('DynamoDB health check failed', error);
      return false;
    }
  }
}

// Create singleton instance
const dynamoDBService = new DynamoDBService();
export default dynamoDBService;