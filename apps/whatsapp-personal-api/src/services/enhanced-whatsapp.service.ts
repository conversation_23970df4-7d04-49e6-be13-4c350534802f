import makeWASocket, {
  DisconnectReason,
  WASocket,
  fetchLatestBaileysVersion,
  makeCacheableSignalKeyStore,
  isJidUser,
  delay,
} from '@whiskeysockets/baileys';
// import { Boom } from '@hapi/boom';
import qrcode from 'qrcode-terminal';
import fs from 'fs';
import { 
  EnhancedWhatsAppSession,
  CreateSessionRequest,
  UpdateSessionRequest,
  SessionStatus,
  WebhookEvent,
  SessionMetadata,
  BulkMessageRequest,
  BulkMessageResult,
  SessionHealth,
  SessionStats
} from '../types';
import config from '../config';
import { sendWebhook } from '../utils/webhook';
import dynamoDBService from './dynamodb.service';
import { createDynamoDBAuthState } from './dynamodb-auth-state.service';
import { createSessionLogger, whatsappLogger } from '../utils/logger';

class EnhancedWhatsAppService {
  private sessions: Map<string, EnhancedWhatsAppSession> = new Map();
  private sessionCleanupInterval?: NodeJS.Timeout;
  private isShuttingDown: boolean = false;

  constructor() {
    this.initializeService();
    this.startSessionCleanup();
  }

  private async initializeService(): Promise<void> {
    try {
      // Create sessions directory if it doesn't exist (fallback)
      if (!fs.existsSync(config.sessionsDir)) {
        fs.mkdirSync(config.sessionsDir, { recursive: true });
      }

      // Load existing sessions from DynamoDB
      await this.loadExistingSessions();
      
      whatsappLogger.serviceStarted();
    } catch (error) {
      whatsappLogger.error('Failed to initialize WhatsApp service', error);
      throw error;
    }
  }

  private async loadExistingSessions(): Promise<void> {
    try {
      const { items: sessionRecords } = await dynamoDBService.getAllSessions({ limit: 100 });
      
      for (const record of sessionRecords) {
        if (record.status === SessionStatus.CONNECTED) {
          try {
            // Attempt to restore connected sessions
            await this.restoreSession(record.sessionId);
          } catch (error) {
            whatsappLogger.warn('Failed to restore session, marking as disconnected', {
              sessionId: record.sessionId,
              error: error instanceof Error ? error.message : String(error)
            });
            
            // Mark session as disconnected
            await dynamoDBService.updateSession(record.sessionId, {
              status: SessionStatus.DISCONNECTED,
              connectionAttempts: record.connectionAttempts + 1
            });
          }
        }
      }
      
      whatsappLogger.info('Existing sessions loaded', { count: sessionRecords.length });
    } catch (error) {
      whatsappLogger.error('Failed to load existing sessions', error);
    }
  }

  private async restoreSession(sessionId: string): Promise<EnhancedWhatsAppSession> {
    try {
      const sessionRecord = await dynamoDBService.getSession(sessionId);
      if (!sessionRecord) {
        throw new Error('Session record not found');
      }

      const logger = createSessionLogger(sessionId, sessionRecord.userId);

      // Create auth state service
      const authStateService = createDynamoDBAuthState(sessionId);
      const { state } = authStateService.createAuthState();

      // Create WhatsApp socket
      const sock = await this.createWhatsAppSocket(state);

      const session: EnhancedWhatsAppSession = {
        id: sessionId,
        name: sessionRecord.sessionName,
        userId: sessionRecord.userId,
        client: sock,
        qr: sessionRecord.qrCode,
        ready: false,
        status: SessionStatus.PENDING,
        phoneNumber: sessionRecord.phoneNumber,
        webhookUrl: sessionRecord.webhookUrl,
        createdAt: new Date(sessionRecord.createdAt),
        lastSeen: sessionRecord.lastSeen ? new Date(sessionRecord.lastSeen) : undefined,
        connectionAttempts: sessionRecord.connectionAttempts,
        metadata: sessionRecord.metadata
      };

      // Set up event handlers
      this.setupSessionEventHandlers(session, logger, authStateService);

      // Store in memory
      this.sessions.set(sessionId, session);

      logger.info('Session restored successfully');
      return session;
    } catch (error) {
      const logger = createSessionLogger(sessionId);
      logger.error('Failed to restore session', error);
      throw error;
    }
  }

  // ===== PUBLIC SESSION MANAGEMENT METHODS =====

  public async createSession(request: CreateSessionRequest): Promise<EnhancedWhatsAppSession> {
    const { id: sessionId, userId, phoneNumber, name, webhookUrl } = request;
    const logger = createSessionLogger(sessionId, userId);

    try {
      // Check if session already exists
      if (this.sessions.has(sessionId)) {
        const existingSession = this.sessions.get(sessionId)!;
        logger.warn('Session already exists', { status: existingSession.status });
        return existingSession;
      }

      // Check user session limits
      await this.checkUserSessionLimits(userId);

      logger.sessionCreated();

      // Create session record in DynamoDB
      const sessionRecord = await dynamoDBService.createSession({
        sessionId,
        userId,
        sessionName: name,
        phoneNumber,
        status: SessionStatus.PENDING,
        webhookUrl,
        expiresAt: Date.now() + (config.session.timeoutHours * 60 * 60 * 1000),
        connectionAttempts: 0,
        metadata: {
          messagesSent: 0,
          messagesReceived: 0,
          webhookCallsSuccess: 0,
          webhookCallsFailed: 0,
          uptime: 0
        }
      });

      // Create auth state service
      const authStateService = createDynamoDBAuthState(sessionId);
      const { state } = authStateService.createAuthState();

      // Create WhatsApp socket
      const sock = await this.createWhatsAppSocket(state);

      const session: EnhancedWhatsAppSession = {
        id: sessionId,
        name,
        userId,
        client: sock,
        ready: false,
        status: SessionStatus.PENDING,
        phoneNumber,
        webhookUrl,
        createdAt: new Date(),
        connectionAttempts: 0,
        metadata: sessionRecord.metadata
      };

      // Set up event handlers
      this.setupSessionEventHandlers(session, logger, authStateService);

      // Store in memory
      this.sessions.set(sessionId, session);

      logger.info('Session created successfully');
      return session;
    } catch (error) {
      logger.error('Failed to create session', error);
      throw error;
    }
  }

  public async getSession(sessionId: string): Promise<EnhancedWhatsAppSession | undefined> {
    // Check memory first
    let session = this.sessions.get(sessionId);
    
    if (!session) {
      // Try to restore from DynamoDB
      try {
        session = await this.restoreSession(sessionId);
      } catch (error) {
        whatsappLogger.debug('Session not found or could not be restored', { sessionId });
        return undefined;
      }
    }

    return session ?? undefined;
  }

  public getAllSessions(): EnhancedWhatsAppSession[] {
    return Array.from(this.sessions.values());
  }

  public async updateSession(sessionId: string, updates: UpdateSessionRequest): Promise<EnhancedWhatsAppSession> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const logger = createSessionLogger(sessionId, session.userId);

    try {
      // Update session in DynamoDB
      await dynamoDBService.updateSession(sessionId, {
        sessionName: updates.name,
        webhookUrl: updates.webhookUrl
      });

      // Update in-memory session
      if (updates.name !== undefined) {
        session.name = updates.name;
      }
      if (updates.webhookUrl !== undefined) {
        session.webhookUrl = updates.webhookUrl;
      }

      logger.info('Session updated', { updates: Object.keys(updates) });
      return session;
    } catch (error) {
      logger.error('Failed to update session', error);
      throw error;
    }
  }

  public async deleteSession(sessionId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId);
    const logger = createSessionLogger(sessionId, session?.userId);

    try {
      if (session) {
        // Close WhatsApp connection
        session.client.end(undefined);

        // Remove from memory
        this.sessions.delete(sessionId);
        logger.sessionDeleted('manual_deletion');
      }

      // Delete from DynamoDB
      await dynamoDBService.deleteSession(sessionId);

      // Clear auth state
      const authStateService = createDynamoDBAuthState(sessionId);
      await authStateService.clearAuthState();

      // Send webhook notification
      if (session?.webhookUrl) {
        await this.sendWebhookNotification(session, WebhookEvent.DELETED, {
          reason: 'manual_deletion'
        });
      }

      logger.info('Session deleted successfully');
      return true;
    } catch (error) {
      logger.error('Failed to delete session', error);
      return false;
    }
  }

  public async reconnectSession(sessionId: string): Promise<EnhancedWhatsAppSession> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const logger = createSessionLogger(sessionId, session.userId);

    try {
      // Close existing connection
      session.client.end(undefined);

      // Update connection attempts
      session.connectionAttempts += 1;
      await dynamoDBService.updateSession(sessionId, {
        connectionAttempts: session.connectionAttempts,
        status: SessionStatus.PENDING
      });

      logger.connectionAttempt(session.connectionAttempts, 5);

      // Create new auth state and socket
      const authStateService = createDynamoDBAuthState(sessionId);
      const { state } = authStateService.createAuthState();
      const sock = await this.createWhatsAppSocket(state);

      // Update session
      session.client = sock;
      session.ready = false;
      session.status = SessionStatus.PENDING;

      // Set up event handlers
      this.setupSessionEventHandlers(session, logger, authStateService);

      logger.info('Session reconnection initiated');
      return session;
    } catch (error) {
      logger.error('Failed to reconnect session', error);
      throw error;
    }
  }

  // ===== MESSAGING METHODS =====

  public async sendTextMessage(sessionId: string, to: string, text: string): Promise<any> {
    const session = await this.getSession(sessionId);
    if (!session || !session.ready) {
      throw new Error('Session not found or not ready');
    }

    const logger = createSessionLogger(sessionId, session.userId);

    try {
      const formattedNumber = this.formatPhoneNumber(to);
      const result = await session.client.sendMessage(formattedNumber, { text });

      // Update metadata
      await this.updateSessionMetadata(sessionId, { messagesSent: session.metadata.messagesSent + 1 });

      logger.messageSent(to, 'text', result?.key?.id || undefined);
      return result;
    } catch (error) {
      logger.error('Failed to send text message', error, { to });
      throw error;
    }
  }

  public async sendMediaMessage(sessionId: string, to: string, url: string, caption?: string, mimetype?: string): Promise<any> {
    const session = await this.getSession(sessionId);
    if (!session || !session.ready) {
      throw new Error('Session not found or not ready');
    }

    const logger = createSessionLogger(sessionId, session.userId);

    try {
      const formattedNumber = this.formatPhoneNumber(to);
      
      const mediaMessage: any = {
        caption: caption || ''
      };

      // Determine media type based on mimetype
      if (mimetype?.startsWith('image/')) {
        mediaMessage.image = { url };
      } else if (mimetype?.startsWith('video/')) {
        mediaMessage.video = { url };
      } else if (mimetype?.startsWith('audio/')) {
        mediaMessage.audio = { url };
      } else {
        mediaMessage.document = { url, mimetype: mimetype || 'application/octet-stream' };
      }

      const result = await session.client.sendMessage(formattedNumber, mediaMessage);

      // Update metadata
      await this.updateSessionMetadata(sessionId, { messagesSent: session.metadata.messagesSent + 1 });

      logger.messageSent(to, 'media', result?.key?.id || undefined);
      return result;
    } catch (error) {
      logger.error('Failed to send media message', error, { to, url });
      throw error;
    }
  }

  public async sendBulkMessages(sessionId: string, request: BulkMessageRequest): Promise<{ success: boolean; results: BulkMessageResult[]; summary: { total: number; sent: number; failed: number } }> {
    const session = await this.getSession(sessionId);
    if (!session || !session.ready) {
      throw new Error('Session not found or not ready');
    }

    const logger = createSessionLogger(sessionId, session.userId);
    const { recipients, message, delay: messageDelay = 1000 } = request;

    logger.bulkMessageStarted(recipients.length);

    const results: BulkMessageResult[] = [];
    let sent = 0;
    let failed = 0;

    for (const recipient of recipients) {
      try {
        let result: any;

        if (message.text) {
          result = await this.sendTextMessage(sessionId, recipient, message.text);
        } else if (message.media) {
          result = await this.sendMediaMessage(sessionId, recipient, message.media.url, message.media.caption, message.media.mimetype);
        } else {
          throw new Error('Message must contain either text or media');
        }

        results.push({
          to: recipient,
          success: true,
          messageId: result?.key?.id
        });
        sent++;

        // Delay between messages to avoid rate limiting
        if (messageDelay > 0 && recipient !== recipients[recipients.length - 1]) {
          await delay(messageDelay);
        }
      } catch (error) {
        results.push({
          to: recipient,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
        failed++;
        logger.error('Failed to send bulk message to recipient', error, { recipient });
      }
    }

    logger.bulkMessageCompleted(sent, failed, recipients.length);

    return {
      success: sent > 0,
      results,
      summary: {
        total: recipients.length,
        sent,
        failed
      }
    };
  }

  // ===== HEALTH AND MONITORING METHODS =====

  public async getSessionHealth(sessionId: string): Promise<SessionHealth> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const uptime = session.createdAt ? Math.floor((Date.now() - session.createdAt.getTime()) / 1000) : 0;
    const issues: string[] = [];

    // Health checks
    if (session.connectionAttempts > 3) {
      issues.push('High connection attempt count');
    }

    if (session.status === SessionStatus.FAILED) {
      issues.push('Session in failed state');
    }

    if (session.lastSeen && (Date.now() - session.lastSeen.getTime()) > 5 * 60 * 1000) {
      issues.push('No recent activity');
    }

    return {
      sessionId,
      status: session.status,
      uptime,
      lastSeen: session.lastSeen,
      connectionAttempts: session.connectionAttempts,
      isHealthy: issues.length === 0 && session.status === SessionStatus.CONNECTED,
      issues
    };
  }

  public async getSessionStats(sessionId: string): Promise<SessionStats> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const today = new Date().toISOString().split('T')[0];
    const statsRecords = await dynamoDBService.getSessionStats(sessionId, today, today);
    const todayStats = statsRecords[0];

    const uptime = session.createdAt ? Math.floor((Date.now() - session.createdAt.getTime()) / 1000) : 0;

    // Calculate webhook success rate
    const totalWebhookCalls = session.metadata.webhookCallsSuccess + session.metadata.webhookCallsFailed;
    const webhookSuccessRate = totalWebhookCalls > 0 
      ? (session.metadata.webhookCallsSuccess / totalWebhookCalls) * 100 
      : 100;

    return {
      sessionId,
      totalMessages: session.metadata.messagesSent + session.metadata.messagesReceived,
      messagesSentToday: todayStats?.messagesSent || 0,
      messagesReceivedToday: todayStats?.messagesReceived || 0,
      webhookSuccessRate,
      averageResponseTime: 0, // Could be implemented with response time tracking
      uptime,
      lastActivity: session.lastSeen
    };
  }

  // ===== PRIVATE HELPER METHODS =====

  private async createWhatsAppSocket(state: any): Promise<WASocket> {
    const { version } = await fetchLatestBaileysVersion();
    
    return makeWASocket({
      auth: {
        creds: state.creds,
        keys: makeCacheableSignalKeyStore(state.keys, { level: 'silent' } as any)
      },
      version,
      printQRInTerminal: config.environment === 'development',
      browser: ['WhatsApp Manager', 'Chrome', '103.0.5060.114'],
      connectTimeoutMs: 30000,
      defaultQueryTimeoutMs: 60000,
      keepAliveIntervalMs: 10000,
      generateHighQualityLinkPreview: true,
      syncFullHistory: false,
      markOnlineOnConnect: true
    });
  }

  private setupSessionEventHandlers(session: EnhancedWhatsAppSession, logger: any, authStateService: any): void {
    const { client } = session;

    // Connection events
    client.ev.on('connection.update', async (update: any) => {
      await this.handleConnectionUpdate(session, update, logger);
    });

    // Credentials update
    client.ev.on('creds.update', async () => {
      try {
        await authStateService.saveAuthState();
      } catch (error) {
        logger.error('Failed to save credentials', error);
      }
    });

    // Message events
    client.ev.on('messages.upsert', async (m: any) => {
      await this.handleMessagesUpsert(session, m, logger);
    });

    // Presence events
    client.ev.on('presence.update', (update: any) => {
      logger.debug('Presence update', { update });
    });
  }

  private async handleConnectionUpdate(session: EnhancedWhatsAppSession, update: any, logger: any): Promise<void> {
    const { connection, lastDisconnect, qr } = update;

    if (qr) {
      logger.qrGenerated(qr);
      session.qr = qr;
      session.status = SessionStatus.PENDING;

      // Update in DynamoDB
      await dynamoDBService.updateSession(session.id, {
        qrCode: qr,
        status: SessionStatus.PENDING
      });

      // Send webhook
      await this.sendWebhookNotification(session, WebhookEvent.QR, { qr });

      // Display QR in terminal for development
      if (config.environment === 'development') {
        qrcode.generate(qr, { small: true });
      }
    }

    if (connection === 'close') {
      const shouldReconnect = (lastDisconnect?.error as any)?.output?.statusCode !== DisconnectReason.loggedOut;
      const reason = lastDisconnect?.error ? lastDisconnect.error.output?.statusCode : 'unknown';

      if (shouldReconnect && session.connectionAttempts < 5 && !this.isShuttingDown) {
        logger.warn('Connection closed, attempting reconnect', { reason, attempt: session.connectionAttempts + 1 });
        
        // Wait before reconnecting
        setTimeout(() => {
          this.reconnectSession(session.id).catch(error => {
            logger.error('Auto-reconnect failed', error);
          });
        }, Math.min(session.connectionAttempts * 5000, 30000));
      } else {
        logger.warn('Connection closed permanently', { reason });
        session.ready = false;
        session.status = reason === DisconnectReason.loggedOut ? SessionStatus.DISCONNECTED : SessionStatus.FAILED;

        // Update in DynamoDB
        await dynamoDBService.updateSession(session.id, {
          status: session.status,
          connectionAttempts: session.connectionAttempts
        });

        // Send webhook
        await this.sendWebhookNotification(session, WebhookEvent.DISCONNECTED, { reason });

        if (reason === DisconnectReason.loggedOut) {
          // Clean up logged out session
          this.sessions.delete(session.id);
        }
      }
    } else if (connection === 'open') {
      logger.connected(session.client.user?.id);
      session.ready = true;
      session.status = SessionStatus.CONNECTED;
      session.lastSeen = new Date();
      session.phoneNumber = session.client.user?.id?.split(':')[0];

      // Reset connection attempts
      session.connectionAttempts = 0;

      // Update in DynamoDB
      await dynamoDBService.updateSession(session.id, {
        status: SessionStatus.CONNECTED,
        phoneNumber: session.phoneNumber,
        connectionAttempts: 0,
        lastSeen: Date.now()
      });

      // Send webhook
      await this.sendWebhookNotification(session, WebhookEvent.CONNECTED, {
        user: session.client.user,
        phoneNumber: session.phoneNumber
      });
    }
  }

  private async handleMessagesUpsert(session: EnhancedWhatsAppSession, m: any, logger: any): Promise<void> {
    if (m.type === 'notify') {
      for (const msg of m.messages) {
        if (!msg.key.fromMe && isJidUser(msg.key.remoteJid || '')) {
          const from = msg.key.remoteJid?.split('@')[0] || 'unknown';
          const messageType = Object.keys(msg.message || {})[0] || 'unknown';

          logger.messageReceived(from, messageType);

          // Update metadata
          await this.updateSessionMetadata(session.id, {
            messagesReceived: session.metadata.messagesReceived + 1
          });

          // Send webhook
          await this.sendWebhookNotification(session, WebhookEvent.MESSAGE, msg);
        }
      }
    }
  }

  private async sendWebhookNotification(session: EnhancedWhatsAppSession, event: WebhookEvent, data: any): Promise<void> {
    if (!session.webhookUrl) return;

    const logger = createSessionLogger(session.id, session.userId);

    try {
      const startTime = Date.now();
      await sendWebhook({
        sessionId: session.id,
        event,
        data,
        timestamp: new Date().toISOString()
      }, session.webhookUrl);

      const responseTime = Date.now() - startTime;
      logger.webhookSent(session.webhookUrl, event, true, responseTime);

      // Update success count
      await this.updateSessionMetadata(session.id, {
        webhookCallsSuccess: session.metadata.webhookCallsSuccess + 1
      });
    } catch (error) {
      logger.webhookSent(session.webhookUrl, event, false);

      // Update failure count
      await this.updateSessionMetadata(session.id, {
        webhookCallsFailed: session.metadata.webhookCallsFailed + 1
      });
    }
  }

  private async updateSessionMetadata(sessionId: string, updates: Partial<SessionMetadata>): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.metadata = { ...session.metadata, ...updates };
    }

    await dynamoDBService.updateSessionMetadata(sessionId, updates);
  }

  private formatPhoneNumber(phoneNumber: string): string {
    const digits = phoneNumber.replace(/\D/g, '');
    
    if (digits.startsWith('0')) {
      return `6${digits.substring(1)}@s.whatsapp.net`;
    } else if (!digits.includes('@')) {
      return `${digits}@s.whatsapp.net`;
    }
    
    return phoneNumber;
  }

  private async checkUserSessionLimits(userId: string): Promise<void> {
    // Get max sessions from config
    const maxSessionsConfig = await dynamoDBService.getConfig('MAX_SESSIONS_PER_USER');
    const maxSessions = maxSessionsConfig ? parseInt(maxSessionsConfig.configValue) : config.session.maxSessionsPerUser;

    // Get user's current sessions
    const { items: userSessions } = await dynamoDBService.getSessionsByUser(userId);
    const activeSessions = userSessions.filter(s => 
      s.status === SessionStatus.CONNECTED || s.status === SessionStatus.PENDING
    );

    if (activeSessions.length >= maxSessions) {
      throw new Error(`User ${userId} has reached maximum sessions limit (${maxSessions})`);
    }
  }

  private startSessionCleanup(): void {
    this.sessionCleanupInterval = setInterval(async () => {
      await this.cleanupExpiredSessions();
    }, config.session.cleanupIntervalSeconds * 1000);

    whatsappLogger.info('Session cleanup scheduler started', {
      intervalSeconds: config.session.cleanupIntervalSeconds
    });
  }

  private async cleanupExpiredSessions(): Promise<void> {
    try {
      const now = Date.now();
      const { items: allSessions } = await dynamoDBService.getAllSessions();
      
      const expiredSessions = allSessions.filter(session => 
        session.expiresAt < now || 
        (session.status === SessionStatus.FAILED && session.connectionAttempts >= 5)
      );

      for (const expiredSession of expiredSessions) {
        whatsappLogger.info('Cleaning up expired session', {
          sessionId: expiredSession.sessionId,
          status: expiredSession.status,
          expired: expiredSession.expiresAt < now
        });

        await this.deleteSession(expiredSession.sessionId);
      }

      if (expiredSessions.length > 0) {
        whatsappLogger.info('Session cleanup completed', { cleaned: expiredSessions.length });
      }
    } catch (error) {
      whatsappLogger.error('Session cleanup failed', error);
    }
  }

  public async shutdown(): Promise<void> {
    whatsappLogger.info('Shutting down WhatsApp service');
    this.isShuttingDown = true;

    // Stop cleanup scheduler
    if (this.sessionCleanupInterval) {
      clearInterval(this.sessionCleanupInterval);
    }

    // Close all sessions
    const closePromises = Array.from(this.sessions.values()).map(async (session) => {
      try {
        session.client.end(undefined);
      } catch (error) {
        whatsappLogger.error('Error closing session during shutdown', error, { sessionId: session.id });
      }
    });

    await Promise.all(closePromises);
    this.sessions.clear();

    whatsappLogger.serviceStopped();
  }
}

// Create singleton instance
const enhancedWhatsappService = new EnhancedWhatsAppService();
export default enhancedWhatsappService;