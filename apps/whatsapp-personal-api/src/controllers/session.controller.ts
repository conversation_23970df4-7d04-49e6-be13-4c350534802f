import express, { Request, Response } from 'express';
import Joi from 'joi';
import enhancedWhatsappService from '../services/enhanced-whatsapp.service';
import monitoringService from '../services/monitoring.service';
import dynamoDBService from '../services/dynamodb.service';
import { createComponentLogger } from '../utils/logger';

const logger = createComponentLogger('SessionController');

// Validation schemas
const createSessionSchema = Joi.object({
  id: Joi.string().required().min(1).max(50).pattern(/^[a-zA-Z0-9_-]+$/),
  userId: Joi.string().required().min(1).max(100),
  phoneNumber: Joi.string().optional().pattern(/^\+?[1-9]\d{1,14}$/),
  name: Joi.string().optional().max(100),
  webhookUrl: Joi.string().uri().optional()
});

const updateSessionSchema = Joi.object({
  name: Joi.string().optional().max(100),
  webhookUrl: Joi.string().uri().optional().allow('')
});

const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
});

// Helper function to create API response
function createResponse(success: boolean, data?: any, error?: string) {
  return {
    success,
    data,
    error: error ? { code: 'API_ERROR', message: error, timestamp: new Date() } : undefined,
    timestamp: new Date()
  };
}

// ===== SESSION MANAGEMENT ENDPOINTS =====

export const createSession = async (req: Request, res: Response): Promise<void> => {
  try {
    const { error, value } = createSessionSchema.validate(req.body);
    if (error) {
      logger.warn('Invalid create session request', { error: error.details[0].message });
      res.status(400).json(createResponse(false, undefined, error.details[0].message));
      return;
    }

    const sessionRequest = value;
    const session = await enhancedWhatsappService.createSession(sessionRequest);

    logger.info('Session created successfully', {
      sessionId: session.id,
      userId: session.userId
    });

    const sessionInfo = {
      id: session.id,
      name: session.name,
      userId: session.userId,
      ready: session.ready,
      status: session.status,
      phoneNumber: session.phoneNumber,
      webhookUrl: session.webhookUrl,
      createdAt: session.createdAt,
      lastSeen: session.lastSeen,
      connectionAttempts: session.connectionAttempts,
      metadata: session.metadata
    };

    // Include QR code in response for new sessions
    const responseData = {
      ...sessionInfo,
      qr: session.qr
    };

    res.status(201).json(createResponse(true, responseData));
  } catch (error: any) {
    logger.error('Failed to create session', error);
    const message = error.message || 'Failed to create session';
    res.status(500).json(createResponse(false, undefined, message));
  }
};

export const getSession = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id) {
      res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
      return;
    }

    const session = await enhancedWhatsappService.getSession(id);
    
    if (!session) {
      res.status(404).json(createResponse(false, undefined, 'Session not found'));
      return;
    }

    const sessionInfo = {
      id: session.id,
      name: session.name,
      userId: session.userId,
      ready: session.ready,
      status: session.status,
      phoneNumber: session.phoneNumber,
      webhookUrl: session.webhookUrl,
      createdAt: session.createdAt,
      lastSeen: session.lastSeen,
      connectionAttempts: session.connectionAttempts,
      metadata: session.metadata
    };

    // Include QR code if session is pending
    const responseData = {
      ...sessionInfo,
      ...(session.qr && { qr: session.qr })
    };

    res.status(200).json(createResponse(true, responseData));
  } catch (error: any) {
    logger.error('Failed to get session', error, { sessionId: req.params.id });
    res.status(500).json(createResponse(false, undefined, 'Failed to get session'));
  }
};

export const getAllSessions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { error, value } = paginationSchema.validate(req.query);
    if (error) {
      res.status(400).json(createResponse(false, undefined, error.details[0].message));
      return;
    }

    const options = value;
    const sessions = enhancedWhatsappService.getAllSessions();

    // Apply pagination
    const startIndex = (options.page - 1) * options.limit;
    const endIndex = startIndex + options.limit;
    const paginatedSessions = sessions.slice(startIndex, endIndex);

    const sessionInfos = paginatedSessions.map(session => ({
      id: session.id,
      name: session.name,
      userId: session.userId,
      ready: session.ready,
      status: session.status,
      phoneNumber: session.phoneNumber,
      webhookUrl: session.webhookUrl,
      createdAt: session.createdAt,
      lastSeen: session.lastSeen,
      connectionAttempts: session.connectionAttempts,
      metadata: session.metadata
    }));

    const totalPages = Math.ceil(sessions.length / options.limit);

    const response = {
      items: sessionInfos,
      pagination: {
        page: options.page,
        limit: options.limit,
        total: sessions.length,
        totalPages,
        hasNext: options.page < totalPages,
        hasPrev: options.page > 1
      }
    };

    res.status(200).json(createResponse(true, response));
  } catch (error: any) {
    logger.error('Failed to get all sessions', error);
    res.status(500).json(createResponse(false, undefined, 'Failed to get sessions'));
  }
};

export const updateSession = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { error, value } = updateSessionSchema.validate(req.body);
    
    if (error) {
      res.status(400).json(createResponse(false, undefined, error.details[0].message));
      return;
    }

    if (!id) {
      res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
      return;
    }

    const updates = value;
    const session = await enhancedWhatsappService.updateSession(id, updates);

    logger.info('Session updated successfully', {
      sessionId: id,
      updates: Object.keys(updates)
    });

    const sessionInfo = {
      id: session.id,
      name: session.name,
      userId: session.userId,
      ready: session.ready,
      status: session.status,
      phoneNumber: session.phoneNumber,
      webhookUrl: session.webhookUrl,
      createdAt: session.createdAt,
      lastSeen: session.lastSeen,
      connectionAttempts: session.connectionAttempts,
      metadata: session.metadata
    };

    res.status(200).json(createResponse(true, sessionInfo));
  } catch (error: any) {
    logger.error('Failed to update session', error, { sessionId: req.params.id });
    const message = error.message || 'Failed to update session';
    res.status(500).json(createResponse(false, undefined, message));
  }
};

export const deleteSession = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id) {
      res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
      return;
    }

    const success = await enhancedWhatsappService.deleteSession(id);
    
    if (!success) {
      res.status(404).json(createResponse(false, undefined, 'Session not found'));
      return;
    }

    logger.info('Session deleted successfully', { sessionId: id });
    res.status(200).json(createResponse(true, { message: 'Session deleted successfully' }));
  } catch (error: any) {
    logger.error('Failed to delete session', error, { sessionId: req.params.id });
    res.status(500).json(createResponse(false, undefined, 'Failed to delete session'));
  }
};

export const reconnectSession = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id) {
      res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
      return;
    }

    const session = await enhancedWhatsappService.reconnectSession(id);

    logger.info('Session reconnection initiated', { sessionId: id });

    const sessionInfo = {
      id: session.id,
      name: session.name,
      userId: session.userId,
      ready: session.ready,
      status: session.status,
      phoneNumber: session.phoneNumber,
      webhookUrl: session.webhookUrl,
      createdAt: session.createdAt,
      lastSeen: session.lastSeen,
      connectionAttempts: session.connectionAttempts,
      metadata: session.metadata
    };

    res.status(200).json(createResponse(true, sessionInfo));
  } catch (error: any) {
    logger.error('Failed to reconnect session', error, { sessionId: req.params.id });
    const message = error.message || 'Failed to reconnect session';
    res.status(500).json(createResponse(false, undefined, message));
  }
};

// ===== SESSION MONITORING ENDPOINTS =====

export const getSessionHealth = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id) {
      res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
      return;
    }

    const health = await enhancedWhatsappService.getSessionHealth(id);
    res.status(200).json(createResponse(true, health));
  } catch (error: any) {
    logger.error('Failed to get session health', error, { sessionId: req.params.id });
    const message = error.message || 'Failed to get session health';
    res.status(500).json(createResponse(false, undefined, message));
  }
};

export const getSessionStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id) {
      res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
      return;
    }

    const stats = await enhancedWhatsappService.getSessionStats(id);
    res.status(200).json(createResponse(true, stats));
  } catch (error: any) {
    logger.error('Failed to get session stats', error, { sessionId: req.params.id });
    const message = error.message || 'Failed to get session stats';
    res.status(500).json(createResponse(false, undefined, message));
  }
};

// ===== USER SESSION MANAGEMENT =====

export const getUserSessions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const { error, value } = paginationSchema.validate(req.query);
    
    if (error) {
      res.status(400).json(createResponse(false, undefined, error.details[0].message));
      return;
    }

    if (!userId) {
      res.status(400).json(createResponse(false, undefined, 'User ID is required'));
      return;
    }

    const options = value;
    const result = await dynamoDBService.getSessionsByUser(userId, options);

    const sessionInfos = result.items.map(record => ({
      id: record.sessionId,
      name: record.sessionName,
      userId: record.userId,
      ready: false, // Would need to check in-memory sessions
      status: record.status,
      phoneNumber: record.phoneNumber,
      webhookUrl: record.webhookUrl,
      createdAt: new Date(record.createdAt),
      lastSeen: record.lastSeen ? new Date(record.lastSeen) : undefined,
      connectionAttempts: record.connectionAttempts,
      metadata: record.metadata
    }));

    const response = {
      items: sessionInfos,
      pagination: result.pagination
    };

    res.status(200).json(createResponse(true, response));
  } catch (error: any) {
    logger.error('Failed to get user sessions', error, { userId: req.params.userId });
    res.status(500).json(createResponse(false, undefined, 'Failed to get user sessions'));
  }
};

// ===== SERVICE MONITORING ENDPOINTS =====

export const getServiceHealth = async (req: Request, res: Response): Promise<void> => {
  try {
    const health = await monitoringService.getServiceHealth();
    res.status(200).json(createResponse(true, health));
  } catch (error: any) {
    logger.error('Failed to get service health', error);
    res.status(500).json(createResponse(false, undefined, 'Failed to get service health'));
  }
};

export const getDetailedMetrics = async (req: Request, res: Response): Promise<void> => {
  try {
    const metrics = await monitoringService.getDetailedServiceMetrics();
    res.status(200).json(createResponse(true, metrics));
  } catch (error: any) {
    logger.error('Failed to get detailed metrics', error);
    res.status(500).json(createResponse(false, undefined, 'Failed to get detailed metrics'));
  }
};

export const getAllSessionsHealth = async (req: Request, res: Response): Promise<void> => {
  try {
    const healthData = await monitoringService.getAllSessionsHealth();
    res.status(200).json(createResponse(true, healthData));
  } catch (error: any) {
    logger.error('Failed to get all sessions health', error);
    res.status(500).json(createResponse(false, undefined, 'Failed to get sessions health'));
  }
};