import express, { Request, Response } from 'express';
import Joi from 'joi';
import enhancedWhatsappService from '../services/enhanced-whatsapp.service';
import { createComponentLogger } from '../utils/logger';

const logger = createComponentLogger('MessageController');

// Validation schemas
const mediaSchema = Joi.object({
  url: Joi.string().uri().required(),
  caption: Joi.string().optional().max(1000),
  mimetype: Joi.string().optional()
});

const sendMessageSchema = Joi.object({
  to: Joi.string().required().pattern(/^\+?[1-9]\d{1,14}$/),
  text: Joi.string().optional().max(4096),
  media: mediaSchema.optional()
}).xor('text', 'media');

const bulkMessageSchema = Joi.object({
  recipients: Joi.array().items(Joi.string().pattern(/^\+?[1-9]\d{1,14}$/)).min(1).max(100).required(),
  message: Joi.object({
    text: Joi.string().optional().max(4096),
    media: mediaSchema.optional()
  }).xor('text', 'media').required(),
  delay: Joi.number().integer().min(0).max(10000).default(1000)
});

// Helper function to create API response
function createResponse(success: boolean, data?: any, error?: string) {
  return {
    success,
    data,
    error: error ? { code: 'API_ERROR', message: error, timestamp: new Date() } : undefined,
    timestamp: new Date()
  };
}

// ===== SINGLE MESSAGE ENDPOINTS =====

export const sendMessage = async (req: Request, res: Response): Promise<void> => {
  try {
    const { sessionId } = req.params;
    
    if (!sessionId) {
      res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
      return;
    }

    const { error, value } = sendMessageSchema.validate(req.body);
    if (error) {
      logger.warn('Invalid send message request', {
        sessionId,
        error: error.details[0].message
      });
      res.status(400).json(createResponse(false, undefined, error.details[0].message));
      return;
    }

    const messageRequest = value;
    let result: any;

    if (messageRequest.text) {
      // Send text message
      result = await enhancedWhatsappService.sendTextMessage(sessionId, messageRequest.to, messageRequest.text);
      
      logger.info('Text message sent', {
        sessionId,
        to: `****${messageRequest.to.slice(-4)}`,
        messageId: result?.key?.id
      });
    } else if (messageRequest.media) {
      // Send media message
      result = await enhancedWhatsappService.sendMediaMessage(
        sessionId, 
        messageRequest.to, 
        messageRequest.media.url, 
        messageRequest.media.caption, 
        messageRequest.media.mimetype
      );
      
      logger.info('Media message sent', {
        sessionId,
        to: `****${messageRequest.to.slice(-4)}`,
        mediaType: messageRequest.media.mimetype,
        messageId: result?.key?.id
      });
    }

    res.status(200).json(createResponse(true, {
      messageId: result?.key?.id,
      timestamp: result?.messageTimestamp,
      status: 'sent'
    }));
  } catch (error: any) {
    logger.error('Failed to send message', error, { sessionId: req.params.sessionId });
    const message = error.message || 'Failed to send message';
    
    // Return appropriate status code based on error type
    const statusCode = error.message?.includes('not found') ? 404 :
                      error.message?.includes('not ready') ? 409 : 500;
    
    res.status(statusCode).json(createResponse(false, undefined, message));
  }
};

// ===== BULK MESSAGING ENDPOINTS =====

export const sendBulkMessages = async (req: Request, res: Response): Promise<void> => {
  try {
    const { sessionId } = req.params;
    
    if (!sessionId) {
      res.status(400).json(createResponse(false, undefined, 'Session ID is required'));
      return;
    }

    const { error, value } = bulkMessageSchema.validate(req.body);
    if (error) {
      logger.warn('Invalid bulk message request', {
        sessionId,
        error: error.details[0].message
      });
      res.status(400).json(createResponse(false, undefined, error.details[0].message));
      return;
    }

    const bulkRequest = value;

    logger.info('Starting bulk message operation', {
      sessionId,
      recipientCount: bulkRequest.recipients.length,
      delay: bulkRequest.delay
    });

    const result = await enhancedWhatsappService.sendBulkMessages(sessionId, bulkRequest);

    logger.info('Bulk message operation completed', {
      sessionId,
      total: result.summary.total,
      sent: result.summary.sent,
      failed: result.summary.failed,
      successRate: result.summary.sent / result.summary.total * 100
    });

    res.status(200).json(createResponse(true, result));
  } catch (error: any) {
    logger.error('Failed to send bulk messages', error, { sessionId: req.params.sessionId });
    const message = error.message || 'Failed to send bulk messages';
    
    const statusCode = error.message?.includes('not found') ? 404 :
                      error.message?.includes('not ready') ? 409 : 500;
    
    res.status(statusCode).json(createResponse(false, undefined, message));
  }
};

// ===== LEGACY ENDPOINTS (for backward compatibility) =====

export const sendTextMessage = async (req: Request, res: Response): Promise<void> => {
  try {
    const { sessionId } = req.params;
    const { to, text } = req.body;

    if (!sessionId || !to || !text) {
      res.status(400).json(createResponse(false, undefined, 'Session ID, recipient, and text are required'));
      return;
    }

    const result = await enhancedWhatsappService.sendTextMessage(sessionId, to, text);

    logger.info('Legacy text message sent', {
      sessionId,
      to: `****${to.slice(-4)}`,
      messageId: result?.key?.id
    });

    res.status(200).json(createResponse(true, {
      messageId: result?.key?.id,
      timestamp: result?.messageTimestamp,
      status: 'sent'
    }));
  } catch (error: any) {
    logger.error('Failed to send text message (legacy)', error, { sessionId: req.params.sessionId });
    const message = error.message || 'Failed to send text message';
    res.status(500).json(createResponse(false, undefined, message));
  }
};

export const sendMediaMessage = async (req: Request, res: Response): Promise<void> => {
  try {
    const { sessionId } = req.params;
    const { to, url, caption, mimetype } = req.body;

    if (!sessionId || !to || !url) {
      res.status(400).json(createResponse(false, undefined, 'Session ID, recipient, and media URL are required'));
      return;
    }

    const result = await enhancedWhatsappService.sendMediaMessage(sessionId, to, url, caption, mimetype);

    logger.info('Legacy media message sent', {
      sessionId,
      to: `****${to.slice(-4)}`,
      mediaType: mimetype,
      messageId: result?.key?.id
    });

    res.status(200).json(createResponse(true, {
      messageId: result?.key?.id,
      timestamp: result?.messageTimestamp,
      status: 'sent'
    }));
  } catch (error: any) {
    logger.error('Failed to send media message (legacy)', error, { sessionId: req.params.sessionId });
    const message = error.message || 'Failed to send media message';
    res.status(500).json(createResponse(false, undefined, message));
  }
};

// ===== MESSAGE VALIDATION ENDPOINTS =====

export const validatePhoneNumber = async (req: Request, res: Response): Promise<void> => {
  try {
    const { phoneNumber } = req.body;

    if (!phoneNumber) {
      res.status(400).json(createResponse(false, undefined, 'Phone number is required'));
      return;
    }

    // Basic phone number validation
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    const isValid = phoneRegex.test(phoneNumber);

    const response = {
      phoneNumber,
      isValid,
      formatted: isValid ? phoneNumber.replace(/\D/g, '') : null,
      countryCode: null // Could be enhanced with phone number parsing library
    };

    res.status(200).json(createResponse(true, response));
  } catch (error: any) {
    logger.error('Failed to validate phone number', error);
    res.status(500).json(createResponse(false, undefined, 'Failed to validate phone number'));
  }
};