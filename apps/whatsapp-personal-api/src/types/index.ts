import { WASocket } from '@whiskeysockets/baileys';

// Legacy types for backward compatibility
export interface WhatsAppSession {
  id: string;
  name?: string;
  client: WASocket;
  qr?: string;
  ready: boolean;
  lastSeen?: Date;
}

export interface WebhookPayload {
  sessionId: string;
  event: string;
  data: any;
  timestamp?: string;
}

export interface SendMessageRequest {
  to: string;
  text?: string;
  media?: {
    url: string;
    caption?: string;
    mimetype?: string;
  };
}

export interface SessionInfo {
  id: string;
  name?: string;
  ready: boolean;
  lastSeen?: Date;
}

// Re-export all enhanced types
export * from './session.types';
