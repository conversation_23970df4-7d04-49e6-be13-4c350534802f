#!/bin/bash

# Start Local Development Environment
# This script starts DynamoDB Local and other development services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting WhatsApp API Local Development Environment${NC}"
echo "============================================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

echo -e "${BLUE}📦 Starting development services...${NC}"

# Start development services (DynamoDB Local first, then others)
echo -e "${BLUE}Starting DynamoDB Local...${NC}"
docker-compose -f docker-compose.simple.yml up -d

# Wait a moment for DynamoDB to start
sleep 5

# Optionally start additional services
if [ "$1" = "--full" ]; then
    echo -e "${BLUE}Starting additional services (Admin UI, Redis)...${NC}"
    docker-compose -f docker-compose.dev.yml up -d dynamodb-admin redis
fi

echo -e "${GREEN}✅ Development services started!${NC}"
echo ""
echo -e "${BLUE}📋 Service Information:${NC}"
echo "• DynamoDB Local: http://localhost:8000"
echo "• DynamoDB Admin UI: http://localhost:8001"
echo "• Redis: localhost:6379"
echo ""

# Wait for DynamoDB Local to be ready
echo -e "${YELLOW}⏳ Waiting for DynamoDB Local to be ready...${NC}"
timeout=60
counter=0

while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:8000 > /dev/null 2>&1; then
        echo -e "${GREEN}✅ DynamoDB Local is ready!${NC}"
        break
    fi
    
    if [ $counter -eq $timeout ]; then
        echo -e "${RED}❌ Timeout waiting for DynamoDB Local to start${NC}"
        exit 1
    fi
    
    sleep 2
    counter=$((counter + 2))
    echo -n "."
done

echo ""
echo -e "${BLUE}🧪 Testing DynamoDB Local connection...${NC}"

# Test DynamoDB Local connection
if curl -X POST http://localhost:8000 \
    -H "Content-Type: application/x-amz-json-1.0" \
    -H "X-Amz-Target: DynamoDB_20120810.ListTables" \
    -d '{}' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ DynamoDB Local connection successful!${NC}"
else
    echo -e "${RED}❌ Failed to connect to DynamoDB Local${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Local development environment is ready!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Start your WhatsApp API application:"
echo "   ${YELLOW}npm run dev${NC} (or ${YELLOW}npm start${NC})"
echo ""
echo "2. Access the services:"
echo "   • Your API: http://localhost:3030"
echo "   • DynamoDB Admin: http://localhost:8001"
echo ""
echo "3. To stop the development environment:"
echo "   ${YELLOW}./scripts/stop-local-dev.sh${NC}"
echo ""
echo -e "${YELLOW}💡 Tip: Check logs with: docker-compose -f docker-compose.dev.yml logs -f${NC}"
