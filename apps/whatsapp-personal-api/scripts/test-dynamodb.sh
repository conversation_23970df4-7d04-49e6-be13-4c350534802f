#!/bin/bash

# Test DynamoDB Local Connection and Operations
# This script tests the DynamoDB Local setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing DynamoDB Local Integration${NC}"
echo "========================================"

# Check if DynamoDB Local is running
echo -e "${BLUE}1. Checking DynamoDB Local connection...${NC}"
if curl -s http://localhost:8000 > /dev/null; then
    echo -e "${GREEN}✅ DynamoDB Local is running on port 8000${NC}"
else
    echo -e "${RED}❌ DynamoDB Local is not running on port 8000${NC}"
    echo -e "${YELLOW}💡 Start it with: ./scripts/start-local-dev.sh${NC}"
    exit 1
fi

# Test basic DynamoDB operations
echo -e "${BLUE}2. Testing basic DynamoDB operations...${NC}"

# List tables
echo -e "${YELLOW}📋 Listing tables...${NC}"
if command -v aws &> /dev/null; then
    aws dynamodb list-tables --endpoint-url http://localhost:8000 --region ap-southeast-1
else
    echo -e "${YELLOW}⚠️  AWS CLI not found. Using curl instead...${NC}"
    curl -X POST http://localhost:8000 \
        -H "Content-Type: application/x-amz-json-1.0" \
        -H "X-Amz-Target: DynamoDB_20120810.ListTables" \
        -d '{}'
fi

echo ""

# Test WhatsApp API health endpoint if it's running
echo -e "${BLUE}3. Testing WhatsApp API integration...${NC}"
if curl -s http://localhost:3030/api/status > /dev/null; then
    echo -e "${GREEN}✅ WhatsApp API is running${NC}"
    
    # Test health endpoint
    echo -e "${YELLOW}🏥 Health check response:${NC}"
    curl -s http://localhost:3030/api/status | jq . 2>/dev/null || curl -s http://localhost:3030/api/status
    echo ""
    
    # Test sessions endpoint
    echo -e "${YELLOW}📱 Sessions endpoint:${NC}"
    curl -s http://localhost:3030/api/sessions | jq . 2>/dev/null || curl -s http://localhost:3030/api/sessions
    echo ""
else
    echo -e "${YELLOW}⚠️  WhatsApp API is not running on port 3030${NC}"
    echo -e "${YELLOW}💡 Start it with: npm run dev${NC}"
fi

echo ""
echo -e "${BLUE}4. Service URLs:${NC}"
echo "• DynamoDB Local: http://localhost:8000"
echo "• DynamoDB Admin UI: http://localhost:8001"
echo "• WhatsApp API: http://localhost:3030"
echo "• Redis: localhost:6379"

echo ""
echo -e "${GREEN}🎉 DynamoDB Local testing completed!${NC}"

# Show useful commands
echo ""
echo -e "${BLUE}💡 Useful commands:${NC}"
echo "• View logs: docker-compose -f docker-compose.dev.yml logs -f"
echo "• Reset data: docker-compose -f docker-compose.dev.yml down -v"
echo "• AWS CLI with local: aws dynamodb list-tables --endpoint-url http://localhost:8000 --region ap-southeast-1"
