#!/bin/bash

# Stop Local Development Environment
# This script stops all development services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🛑 Stopping WhatsApp API Local Development Environment${NC}"
echo "=========================================================="

# Stop development services
echo -e "${BLUE}📦 Stopping development services...${NC}"
docker-compose -f docker-compose.dev.yml down

echo -e "${GREEN}✅ Development services stopped!${NC}"
echo ""
echo -e "${YELLOW}💡 To remove all data (reset everything):${NC}"
echo "   ${YELLOW}docker-compose -f docker-compose.dev.yml down -v${NC}"
echo ""
echo -e "${YELLOW}💡 To start again:${NC}"
echo "   ${YELLOW}./scripts/start-local-dev.sh${NC}"
