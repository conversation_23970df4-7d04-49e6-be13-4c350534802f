#!/bin/bash

# Test WhatsApp API Integration with DynamoDB Local
# This script tests the complete integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing WhatsApp API Integration with DynamoDB Local${NC}"
echo "=========================================================="

# Test 1: Check DynamoDB Local
echo -e "${BLUE}1. Testing DynamoDB Local connection...${NC}"
if curl -s http://localhost:8000 > /dev/null; then
    echo -e "${GREEN}✅ DynamoDB Local is running on port 8000${NC}"
    
    # Test DynamoDB API
    response=$(curl -s -X POST http://localhost:8000 \
        -H "Content-Type: application/x-amz-json-1.0" \
        -H "X-Amz-Target: DynamoDB_20120810.ListTables" \
        -H "Authorization: AWS4-HMAC-SHA256 Credential=dummy/20240101/ap-southeast-1/dynamodb/aws4_request, SignedHeaders=host;x-amz-date, Signature=dummy" \
        -H "X-Amz-Date: 20240101T000000Z" \
        -d '{}')
    
    if echo "$response" | grep -q "TableNames"; then
        echo -e "${GREEN}✅ DynamoDB Local API is responding correctly${NC}"
        echo -e "${YELLOW}📋 Current tables: $response${NC}"
    else
        echo -e "${RED}❌ DynamoDB Local API error: $response${NC}"
    fi
else
    echo -e "${RED}❌ DynamoDB Local is not running on port 8000${NC}"
    echo -e "${YELLOW}💡 Start it with: npm run local:start${NC}"
    exit 1
fi

echo ""

# Test 2: Check WhatsApp API
echo -e "${BLUE}2. Testing WhatsApp API connection...${NC}"
if curl -s http://localhost:3001/health > /dev/null; then
    echo -e "${GREEN}✅ WhatsApp API is running on port 3001${NC}"
    
    # Test health endpoint
    health_response=$(curl -s http://localhost:3001/health)
    echo -e "${YELLOW}🏥 Health check: $health_response${NC}"
    
    # Test status endpoint
    status_response=$(curl -s http://localhost:3001/api/status)
    echo -e "${YELLOW}📊 Status: $status_response${NC}"
    
else
    echo -e "${RED}❌ WhatsApp API is not running on port 3001${NC}"
    echo -e "${YELLOW}💡 Start it with: npm run dev${NC}"
    exit 1
fi

echo ""

# Test 3: Test DynamoDB Integration
echo -e "${BLUE}3. Testing DynamoDB integration...${NC}"

# Test sessions endpoint
sessions_response=$(curl -s http://localhost:3001/api/sessions)
if echo "$sessions_response" | grep -q "success"; then
    echo -e "${GREEN}✅ Sessions endpoint is working${NC}"
    echo -e "${YELLOW}📱 Sessions: $sessions_response${NC}"
else
    echo -e "${RED}❌ Sessions endpoint error: $sessions_response${NC}"
fi

echo ""

# Test 4: Create a test session
echo -e "${BLUE}4. Testing session creation...${NC}"

test_session_data='{
    "userId": "test-user-'$(date +%s)'",
    "sessionName": "Test Session",
    "phoneNumber": "+1234567890"
}'

create_response=$(curl -s -X POST http://localhost:3001/api/sessions \
    -H "Content-Type: application/json" \
    -d "$test_session_data")

if echo "$create_response" | grep -q "success"; then
    echo -e "${GREEN}✅ Session creation is working${NC}"
    echo -e "${YELLOW}📱 Created session: $create_response${NC}"
    
    # Extract session ID for cleanup
    session_id=$(echo "$create_response" | grep -o '"sessionId":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$session_id" ]; then
        echo -e "${BLUE}5. Testing session retrieval...${NC}"
        get_response=$(curl -s http://localhost:3001/api/sessions/$session_id)
        
        if echo "$get_response" | grep -q "success"; then
            echo -e "${GREEN}✅ Session retrieval is working${NC}"
            echo -e "${YELLOW}📱 Retrieved session: $get_response${NC}"
        else
            echo -e "${RED}❌ Session retrieval error: $get_response${NC}"
        fi
        
        # Clean up test session
        echo -e "${BLUE}6. Cleaning up test session...${NC}"
        delete_response=$(curl -s -X DELETE http://localhost:3001/api/sessions/$session_id)
        
        if echo "$delete_response" | grep -q "success"; then
            echo -e "${GREEN}✅ Session deletion is working${NC}"
        else
            echo -e "${YELLOW}⚠️  Session deletion response: $delete_response${NC}"
        fi
    fi
else
    echo -e "${RED}❌ Session creation error: $create_response${NC}"
fi

echo ""

# Summary
echo -e "${GREEN}🎉 Integration testing completed!${NC}"
echo ""
echo -e "${BLUE}📋 Service URLs:${NC}"
echo "• WhatsApp API: http://localhost:3001"
echo "• DynamoDB Local: http://localhost:8000"
echo "• Health Check: http://localhost:3001/health"
echo "• Sessions API: http://localhost:3001/api/sessions"

echo ""
echo -e "${BLUE}💡 Next steps:${NC}"
echo "• Your WhatsApp API is successfully integrated with DynamoDB Local"
echo "• You can now create, retrieve, update, and delete sessions"
echo "• All data is stored locally in DynamoDB Local"
echo "• Use the API endpoints to manage WhatsApp sessions"

echo ""
echo -e "${YELLOW}🔧 Useful commands:${NC}"
echo "• View API logs: Check the terminal where you ran 'npm run dev'"
echo "• View DynamoDB logs: docker logs whatsapp-dynamodb-local"
echo "• Reset data: npm run local:reset && npm run local:start"
