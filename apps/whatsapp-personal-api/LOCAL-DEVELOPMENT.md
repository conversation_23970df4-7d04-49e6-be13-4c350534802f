# WhatsApp Personal API - Local Development Setup

This guide will help you set up and run the WhatsApp Personal API with DynamoDB Local for development and testing.

## 🚀 Quick Start

### Prerequisites
- <PERSON>er and Docker Compose
- Node.js (v18 or higher)
- npm or yarn

### 1. Start Local Development Environment

```bash
# Start DynamoDB Local and supporting services
npm run local:start

# In another terminal, start the API
npm run dev
```

### 2. Test the Setup

```bash
# Test DynamoDB Local and API integration
npm run local:test
```

### 3. Access Services

- **WhatsApp API**: http://localhost:3030
- **DynamoDB Admin UI**: http://localhost:8001
- **DynamoDB Local**: http://localhost:8000

## 📋 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run local:start` | Start DynamoDB Local and supporting services |
| `npm run local:stop` | Stop all local development services |
| `npm run local:test` | Test DynamoDB Local connection and API integration |
| `npm run local:reset` | Reset all data (removes volumes) |
| `npm run local:logs` | View logs from all services |
| `npm run dev` | Start the API in development mode |

## 🏗 Architecture

### Services

1. **DynamoDB Local** (Port 8000)
   - Local DynamoDB instance for development
   - Data persisted in Docker volume
   - Compatible with AWS DynamoDB API

2. **DynamoDB Admin UI** (Port 8001)
   - Web interface for managing DynamoDB tables
   - View, create, and modify table data
   - Query and scan operations

3. **Redis** (Port 6379)
   - Optional caching and rate limiting
   - Persistent data storage

4. **WhatsApp API** (Port 3030)
   - Your main application
   - Connects to DynamoDB Local

### Tables Created Automatically

- `whatsapp-sessions-local` - WhatsApp session data
- `whatsapp-auth-state-local` - Authentication states
- `whatsapp-config-local` - Configuration settings
- `whatsapp-stats-local` - Usage statistics

## 🔧 Configuration

### Environment Files

- `.env.local` - Local development configuration
- `.env.example` - Template for production configuration

### Key Configuration Variables

```bash
# DynamoDB Local
DYNAMODB_ENDPOINT=http://localhost:8000
AWS_ACCESS_KEY_ID=dummy
AWS_SECRET_ACCESS_KEY=dummy
AWS_REGION=ap-southeast-1

# Tables
DYNAMODB_SESSION_TABLE=whatsapp-sessions-local
DYNAMODB_AUTH_STATE_TABLE=whatsapp-auth-state-local
DYNAMODB_CONFIG_TABLE=whatsapp-config-local
DYNAMODB_STATS_TABLE=whatsapp-stats-local
```

## 🧪 Testing

### Manual Testing

1. **Health Check**
   ```bash
   curl http://localhost:3030/api/status
   ```

2. **List Sessions**
   ```bash
   curl http://localhost:3030/api/sessions
   ```

3. **DynamoDB Tables**
   ```bash
   # Using AWS CLI
   aws dynamodb list-tables --endpoint-url http://localhost:8000 --region ap-southeast-1
   
   # Using curl
   curl -X POST http://localhost:8000 \
     -H "Content-Type: application/x-amz-json-1.0" \
     -H "X-Amz-Target: DynamoDB_20120810.ListTables" \
     -d '{}'
   ```

### Using DynamoDB Admin UI

1. Open http://localhost:8001
2. Browse tables and data
3. Run queries and scans
4. Create/modify table items

## 🐛 Troubleshooting

### Common Issues

1. **Port 8000 already in use**
   ```bash
   # Find what's using the port
   lsof -i :8000
   
   # Kill the process or modify docker-compose.dev.yml
   ```

2. **DynamoDB Local not starting**
   ```bash
   # Check Docker logs
   docker-compose -f docker-compose.dev.yml logs dynamodb-local
   
   # Restart services
   npm run local:stop
   npm run local:start
   ```

3. **API can't connect to DynamoDB**
   - Ensure DynamoDB Local is running: `curl http://localhost:8000`
   - Check environment variables in `.env.local`
   - Verify Docker network connectivity

4. **Tables not created**
   - Check API logs for DynamoDB errors
   - Verify AWS credentials (dummy values are fine for local)
   - Ensure proper table names in configuration

### Reset Everything

```bash
# Stop services and remove all data
npm run local:reset

# Start fresh
npm run local:start
```

## 📊 Monitoring

### View Logs

```bash
# All services
npm run local:logs

# Specific service
docker-compose -f docker-compose.dev.yml logs -f dynamodb-local
docker-compose -f docker-compose.dev.yml logs -f dynamodb-admin
```

### Health Checks

- DynamoDB Local: http://localhost:8000
- API Health: http://localhost:3030/api/status
- Admin UI: http://localhost:8001

## 🔄 Development Workflow

1. **Start Development Environment**
   ```bash
   npm run local:start
   ```

2. **Start API in Development Mode**
   ```bash
   npm run dev
   ```

3. **Make Changes and Test**
   - API automatically reloads with nodemon
   - DynamoDB data persists between restarts
   - Use Admin UI to inspect data

4. **Reset Data When Needed**
   ```bash
   npm run local:reset
   npm run local:start
   ```

5. **Stop Everything**
   ```bash
   npm run local:stop
   ```

## 🚢 Production Deployment

When deploying to production:

1. Update environment variables to use real AWS DynamoDB
2. Remove `DYNAMODB_ENDPOINT` to use AWS service
3. Configure proper AWS credentials
4. Update table names for production environment

## 📚 Additional Resources

- [DynamoDB Local Documentation](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DynamoDBLocal.html)
- [AWS SDK for JavaScript v3](https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)

---

🎉 **Happy Coding!** Your local DynamoDB development environment is ready to use.
